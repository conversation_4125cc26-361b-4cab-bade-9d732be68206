#!/bin/bash

# Exit on error
set -e

# Fixed image name (same as in docker-compose)
IMAGE_NAME="herbit-unified"

# Tag can be passed as argument or set default
TAG="${1:-latest}"

echo "🔧 Using tag: $TAG"
echo "🛠️  Building Docker image: $IMAGE_NAME:$TAG..."
docker build -t "$IMAGE_NAME:$TAG" .

echo "🚀 Starting docker-compose with tag: $TAG..."
TAG=$TAG docker-compose up --build -d

echo "✅ Done! Services are running with image: $IMAGE_NAME:$TAG"
