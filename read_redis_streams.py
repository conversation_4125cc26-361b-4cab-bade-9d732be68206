import json
import subprocess
from datetime import datetime
from typing import Dict, List, Optional

CONTAINER_NAME = "herbit-redis-messagebus"
STREAMS = ["question-tasks", "task-dlq"]


def run_redis_cli(*args):
    try:
        result = subprocess.check_output(
            ["docker", "exec", "-i", CONTAINER_NAME, "redis-cli", "--raw", *args],
            stderr=subprocess.STDOUT,
            text=True,
        )
        return result.strip().split("\n")
    except subprocess.CalledProcessError as e:
        print(f"Error: {e.output}")
        return []


def parse_stream_output(lines):
    messages = []
    i = 0
    while i < len(lines):
        if i >= len(lines):
            break
        msg_id = lines[i]
        i += 1
        fields = {}
        while i + 1 < len(lines) and not lines[i].endswith("-0"):
            key = lines[i]
            value = lines[i + 1]
            fields[key] = value
            i += 2
        messages.append((msg_id, fields))
    return messages


def try_json(s):
    try:
        return json.loads(s)
    except Exception:
        return s


def get_task_status(task_id: str) -> Optional[Dict]:
    """
    Get task status from Dapr state store via Redis
    Tasks are stored with key format: herbit||task-{task_id} as Redis hashes
    """
    try:
        # Get hash data from Dapr state store
        result = run_redis_cli("HGETALL", f"herbit||task-{task_id}")
        if result and result != [""]:
            # Convert list of key-value pairs to dict
            hash_data = {}
            for i in range(0, len(result), 2):
                if i + 1 < len(result):
                    key = result[i]
                    value = result[i + 1]
                    # Try to parse JSON values
                    hash_data[key] = try_json(value)

            # Extract the actual task data from the 'data' field
            if "data" in hash_data and isinstance(hash_data["data"], dict):
                return hash_data["data"]
            return hash_data if hash_data else None
    except Exception as e:
        print(f"Error getting task status: {e}")
    return None


def get_all_task_statuses() -> List[Dict]:
    """
    Get all task statuses from Dapr state store
    """
    try:
        # Get all keys that start with 'herbit||task-'
        keys = run_redis_cli("KEYS", "herbit||task-*")
        if not keys or keys == [""]:
            return []

        task_statuses = []
        for key in keys:
            if key.startswith("herbit||task-"):
                # Extract task_id from key
                task_id = key.replace("herbit||task-", "")
                task_data = get_task_status(task_id)
                if task_data:
                    task_statuses.append(task_data)

        return task_statuses
    except Exception as e:
        print(f"Error getting all task statuses: {e}")
        return []


def format_timestamp(timestamp_str: str) -> str:
    """Format ISO timestamp to readable format"""
    try:
        dt = datetime.fromisoformat(timestamp_str.replace("Z", "+00:00"))
        return dt.strftime("%Y-%m-%d %H:%M:%S UTC")
    except Exception as e:
        print(f"Timestamp formatting error: {e}")
        return timestamp_str


def get_task_summary() -> Dict:
    """Get a summary of all tasks"""
    task_statuses = get_all_task_statuses()

    summary = {
        "total_tasks": len(task_statuses),
        "completed": 0,
        "processing": 0,
        "failed": 0,
        "total_questions_generated": 0,
        "skills_processed": set(),
    }

    for task in task_statuses:
        status = task.get("status", "unknown")
        summary[status] = summary.get(status, 0) + 1

        if status == "completed":
            summary["total_questions_generated"] += task.get("questions_generated", 0)

        skill_name = task.get("skill_name")
        if skill_name:
            summary["skills_processed"].add(skill_name)

    summary["skills_processed"] = list(summary["skills_processed"])
    return summary


def show_task_status_info(task_data: Dict):
    """Show detailed task status information"""
    task_id = task_data.get("task_id", "Unknown")
    print(f"\n Checking task status for: {task_id}")

    status = get_task_status(task_id)
    if status:
        print(" Task Status:")
        print(f"    Status: {status.get('status', 'unknown').upper()}")
        print(f"    Skill: {status.get('skill_name', 'N/A')} (ID: {status.get('skill_id', 'N/A')})")
        print(f"    Questions Generated: {status.get('questions_generated', 0)}")
        print(f"   ⏰ Timestamp: {format_timestamp(status.get('timestamp', ''))}")

        error_msg = status.get("error_message")
        if error_msg:
            print(f"    Error: {error_msg}")
    else:
        print("    No status information found in Dapr state store")


def check_specific_task(task_id: str):
    """Check the status of a specific task"""
    print(f" CHECKING TASK: {task_id}")
    print("=" * 50)

    status = get_task_status(task_id)
    if status:
        print(" Task Status Information:")
        print(f"    Task ID: {status.get('task_id', 'N/A')}")
        print(f"    Status: {status.get('status', 'unknown').upper()}")
        print(f"    Skill: {status.get('skill_name', 'N/A')} (ID: {status.get('skill_id', 'N/A')})")
        print(f"    Questions Generated: {status.get('questions_generated', 0)}")
        print(f"   ⏰ Last Updated: {format_timestamp(status.get('timestamp', ''))}")

        error_msg = status.get("error_message")
        if error_msg:
            print(f"    Error: {error_msg}")

        # Additional info for completed tasks
        if status.get("status") == "completed":
            print("    Task completed successfully!")
        elif status.get("status") == "processing":
            print("   ⏳ Task is still being processed...")
        elif status.get("status") == "failed":
            print("    Task failed to complete")
    else:
        print(f"    No task found with ID: {task_id}")
        print("    Make sure the task ID is correct and the task exists")


def main():
    import sys

    # Check if user wants to check a specific task
    if len(sys.argv) > 2 and sys.argv[1] == "--task":
        task_id = sys.argv[2]
        check_specific_task(task_id)
        return

    # Check if user wants summary
    if len(sys.argv) > 1 and sys.argv[1] == "--summary":
        print(" TASK SUMMARY")
        print("=" * 50)
        summary = get_task_summary()
        print(f"Total Tasks: {summary['total_tasks']}")
        print(f" Completed: {summary['completed']}")
        print(f" Processing: {summary['processing']}")
        print(f" Failed: {summary['failed']}")
        print(f" Total Questions Generated: {summary['total_questions_generated']}")
        print(f" Skills Processed: {len(summary['skills_processed'])}")

        if summary["skills_processed"]:
            print("\nSkills:")
            for skill in summary["skills_processed"]:
                print(f"  - {skill}")

        print("\n" + "=" * 50)
        print(" DETAILED TASK STATUS")
        print("=" * 50)

        all_statuses = get_all_task_statuses()
        for task_status in sorted(all_statuses, key=lambda x: x.get("timestamp", ""), reverse=True):
            print(f"\n Task: {task_status.get('task_id', 'Unknown')}")
            print(f"   Status: {task_status.get('status', 'unknown').upper()}")
            print(f"   Skill: {task_status.get('skill_name', 'N/A')}")
            print(f"   Questions: {task_status.get('questions_generated', 0)}")
            print(f"   Time: {format_timestamp(task_status.get('timestamp', ''))}")
            if task_status.get("error_message"):
                print(f"   Error: {task_status.get('error_message')}")
        return

    # Show streams and check task status
    print(" REDIS STREAMS MONITORING")
    print("=" * 60)

    for stream in STREAMS:
        print(f"\n Stream: {stream}")
        lines = run_redis_cli("XRANGE", stream, "-", "+")
        if not lines or lines == [""]:
            print("  (no messages)")
            continue

        messages = parse_stream_output(lines)
        for msg_id, fields in messages:
            print(f"\n Message ID: {msg_id}")

            # Extract task data for status checking
            task_data = {}
            for field, value in fields.items():
                value_decoded = try_json(value)
                if isinstance(value_decoded, dict):
                    print(f" {field}:")
                    print(json.dumps(value_decoded, indent=2))
                    # Extract task_id from nested data structure
                    if "data" in value_decoded and isinstance(value_decoded["data"], dict):
                        if "task_id" in value_decoded["data"]:
                            task_data = value_decoded["data"]
                    elif "task_id" in value_decoded:
                        task_data = value_decoded
                else:
                    print(f" {field}: {value_decoded}")

            # Show task status if we found a task_id
            if task_data.get("task_id"):
                show_task_status_info(task_data)

        print("-" * 60)

    # Show overall summary at the end
    print("\n QUICK SUMMARY")
    print("=" * 30)
    summary = get_task_summary()
    print(f"Active Tasks: {summary['processing']}")
    print(f"Completed: {summary['completed']}")
    print(f"Failed: {summary['failed']}")
    print(f"Total Questions Generated: {summary['total_questions_generated']}")

    print("\n USAGE TIPS:")
    print("   - Use 'python read_redis_streams.py --summary' for detailed task status")
    print("   - Use 'python read_redis_streams.py --task <task_id>' to check specific task")
    print("   - Use 'python read_redis_streams.py' to monitor streams with task status")


if __name__ == "__main__":
    main()
