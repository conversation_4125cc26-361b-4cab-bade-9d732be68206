"""
Quiz-related API routes for the quiz/assessment management system.
"""

from typing import Any, Dict, List, Optional

from database.db import get_db, get_db_context
from database.models import Session
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from ...api.middlewares.hashid_middleware import hash_ids_in_response
from ...models.assessment_manager import (
    get_session_and_assessment_details_by_code,
)
from ...models.db_manager import (
    insert_user_data,
)
from ...models.quiz_manager import (
    db_get_question_by_id,
    db_upsert_user_answer,
    get_all_questions_for_assessment,
    get_next_dynamic_question_from_db,
    get_questions_for_check,
)
from ...models.sessions_manager import (
    calculate_session_scores,
    complete_session_in_db,
    get_or_create_user,
    get_session_for_start_or_validation,
    get_session_response_data,
    handle_completed_session,
    handle_expired_session,
    start_session_in_db,
    validate_session_and_user,
    validate_session_code_format,
)
from ...utils.api_response import (
    raise_http_exception,
    success_response,
)
from ...utils.db_utils import (
    safe_json_dumps,
    safe_json_loads,
)
from ...utils.hashid_utils import (
    decode_skill_id,
    hash_answer,
)
from ...utils.logger import (
    debug,
    error,
    info,
)
from ...utils.performance_utils import (
    calculate_question_score,
)
from ...utils.rate_limiter import rate_limiter
from ...utils.session_utils import (
    get_session_details,
)


# Define Pydantic models for request validation
class AnswerRequest(BaseModel):
    user_id: str
    question_id: str  # This is que_id from questions table
    answer: str
    session_code: str  # Changed from quiz_code
    time_taken: Optional[int] = None  # Time taken in seconds


class UserCheckRequest(BaseModel):
    user_id: str


class TaskResponse(BaseModel):
    """Response model for task submission"""

    task_id: str
    status: str
    message: str


class DynamicQuestionRequest(BaseModel):
    session_code: str
    difficulty: str
    exclude_ids: List[int] = []
    skill_hashes: List[str]


class QuestionResponse(BaseModel):
    # Use 'Any' if que_id can be str or int, or be more specific if you know.
    que_id: Any
    question: str
    options: Dict[str, str]


class DynamicQuestionResponse(BaseModel):
    question: Optional[Dict]


class AllQuestionsApiResponse(BaseModel):
    questions: List[QuestionResponse]
    remaining_time_seconds: int
    attempted_answers: Dict[str, str] = {}  # Default to empty dict


quiz_router = APIRouter(prefix="/quiz")


# =============================================================================
# QUIZ SESSION MANAGEMENT ENDPOINTS
# =============================================================================


class ValidateCodeRequest(BaseModel):
    session_code: str


class StartExamRequest(BaseModel):
    session_code: str


class SubmitExamRequest(BaseModel):
    session_code: str
    user_id: str


@quiz_router.post("/validate_code")
def validate_session_code_endpoint(request: ValidateCodeRequest):
    """
    Validate if a session code is valid and return session information.
    """
    session_code_input = request.session_code
    if not session_code_input:
        raise_http_exception(status_code=400, detail="Session code is required.")

    try:
        session_code = validate_session_code_format(session_code_input)

        session_details = get_session_for_start_or_validation(session_code)

        if not session_details:
            raise_http_exception(status_code=404, detail="Invalid or expired session code.")

        # All details, including question_selection_mode, are now in one object
        response_data = {
            "session_id": session_details["id"],
            "session_code": request.session_code,
            "assessment_id": session_details["assessment_id"],
            "assessment_name": session_details["assessment_name"],
            "is_final": session_details["is_final"],
            "username": session_details.get("username", ""),
            "session_status": session_details["session_status"],
            "remaining_time_seconds": session_details.get("remaining_time_seconds", 0),
            "started_at": session_details["started_at"].isoformat() if session_details["started_at"] else None,
            "completed_at": session_details["completed_at"].isoformat() if session_details["completed_at"] else None,
            "question_selection_mode": session_details["question_selection_mode"],
        }
        hashed_data = hash_ids_in_response(response_data)
        return success_response(data=hashed_data, message="Session code validated successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error validating session code {session_code_input}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Server error: {str(e)}")


@quiz_router.post("/start")
def start_exam_endpoint(request: StartExamRequest):
    """
    Start an exam session.
    """
    session_code_input = request.session_code
    if not session_code_input:
        raise_http_exception(status_code=400, detail="Session code is required.")

    try:
        session_code = validate_session_code_format(session_code_input)
        # 1. Get session details to validate
        session_details = get_session_for_start_or_validation(session_code)

        if not session_details:
            raise_http_exception(status_code=404, detail="Invalid or expired session code.")

        if session_details["session_status"] == "completed":
            raise_http_exception(
                status_code=409,
                detail=f"Session cannot be started. Current status: {session_details['session_status']}",
            )
        elif session_details["session_status"] == "in_progress":
            # Session is already in progress, this is okay - just return success
            info(f"Session {session_code} is already in progress, returning current state")
            response_data = {
                "session_id": session_details["id"],
                "session_code": session_code,
                "assessment_id": session_details["assessment_id"],
                "assessment_name": session_details["assessment_name"],
                "is_final": session_details["is_final"],
                "username": session_details.get("username", ""),
                "session_status": session_details["session_status"],
                "started_at": (session_details["started_at"].isoformat() if session_details["started_at"] else None),
                "completed_at": (
                    session_details["completed_at"].isoformat() if session_details["completed_at"] else None
                ),
            }
            hashed_data = hash_ids_in_response(response_data)
            return success_response(data=hashed_data, message="Session already in progress")
        elif session_details["session_status"] != "pending":
            raise_http_exception(
                status_code=409,
                detail=f"Session cannot be started. Current status: {session_details['session_status']}",
            )

        # 2. Update the session in the database
        rows_updated = start_session_in_db(session_details["id"])
        if rows_updated == 0:
            raise_http_exception(status_code=500, detail="Failed to start session, status might have changed.")

        # 3. Refresh session details to get updated timestamps
        updated_session_details = get_session_for_start_or_validation(session_code)

        # Format and return the response
        response_data = {
            "session_id": updated_session_details["id"],
            "session_code": session_code,
            "assessment_id": updated_session_details["assessment_id"],
            "assessment_name": updated_session_details["assessment_name"],
            "is_final": updated_session_details["is_final"],
            "username": updated_session_details.get("username", ""),
            "session_status": updated_session_details["session_status"],
            "started_at": (
                updated_session_details["started_at"].isoformat() if updated_session_details["started_at"] else None
            ),
            "completed_at": (
                updated_session_details["completed_at"].isoformat() if updated_session_details["completed_at"] else None
            ),
        }
        hashed_data = hash_ids_in_response(response_data)
        return success_response(data=hashed_data, message="Session started successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error starting session {session_code_input}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Server error: {str(e)}")


def _validate_submit_session_request(session_code_input: str, user_id: str) -> str:
    """Validate submit session request parameters."""
    if not session_code_input:
        raise_http_exception(status_code=400, detail="Session code is required.")
    if not user_id:
        raise_http_exception(status_code=400, detail="User ID is required.")

    session_code = validate_session_code_format(session_code_input)
    debug(f"Decoded session code: '{session_code}'")
    return session_code


def _get_session_details_for_submit(session_code: str) -> dict:
    """Get session details and validate they exist."""
    session_details = get_session_and_assessment_details_by_code(session_code)
    if not session_details:
        debug(f"Session not found for code: '{session_code}'")
        raise_http_exception(status_code=404, detail="Invalid or expired session code.")

    debug(
        f"Session details: status='{session_details['session_status']}', "
        f"session_id={session_details.get('session_id')}"
    )
    return session_details


def _validate_session_user(session_details: dict, user_id: str):
    """Validate that user matches the session."""
    internal_user_id = get_or_create_user(user_id)
    debug(f"User validation: internal_user_id={internal_user_id}, session_user_id={session_details.get('user_id')}")

    if session_details["user_id"] != internal_user_id:
        debug(
            f"User mismatch: session belongs to user_id={session_details['user_id']}, "
            f"but request from user_id={internal_user_id}"
        )
        raise_http_exception(status_code=403, detail="User does not match session.")


@quiz_router.post("/submit")
def submit_exam_endpoint(request: SubmitExamRequest, _: None = Depends(rate_limiter)):
    """
    Submit a quiz session, marking it as completed and calculating final score.
    """
    try:
        session_code_input = request.session_code
        user_id = request.user_id

        debug(f"Submit session request: session_code='{session_code_input}', user_id='{user_id}'")

        # Validate request parameters
        session_code = _validate_submit_session_request(session_code_input, user_id)

        # Get session details
        session_details = _get_session_details_for_submit(session_code)

        # Handle different session statuses
        current_status = session_details["session_status"]

        if current_status == "completed":
            return handle_completed_session(session_details)
        elif current_status == "expired":
            return handle_expired_session(session_details)
        elif current_status == "pending":
            # Auto-start the session if it's still pending
            debug(f"Session is pending, auto-starting session: {session_details.get('session_id')}")
            start_session_in_db(session_details["session_id"])
            session_details["session_status"] = "in_progress"
        elif current_status != "in_progress":
            debug(f"Session not in progress. Current status: {current_status}")
            raise_http_exception(
                status_code=400, detail=f"Session is not in progress. Current status: {current_status}"
            )

        # Validate user matches session
        _validate_session_user(session_details, user_id)

        # Calculate scores
        obtained_score, total_possible_score, performance_level = calculate_session_scores(session_details)

        # Complete session in database
        complete_session_in_db(session_details, obtained_score)

        # Calculate percentage and prepare response
        percentage = (obtained_score / total_possible_score * 100) if total_possible_score > 0 else 0

        data = {
            "session_id": session_details["session_id"],
            "obtained_score": obtained_score,
            "total_possible_score": total_possible_score,
            "percentage": round(percentage, 2),
            "performance_level": performance_level,
            "status": "completed",
        }
        hashed_data = hash_ids_in_response(data)
        return success_response(data=hashed_data, message="Session submitted successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error submitting session {session_code_input}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Error submitting session: {str(e)}")


# =============================================================================
# CHECK AND SAVE ANSWER API ENDPOINT AND HELPER FUNCTIONS
# =============================================================================


def _determine_result_status(answer: str, is_correct: bool) -> str:
    """Determine result status based on answer and correctness."""
    lowercased_answer = answer.lower()
    if lowercased_answer == "timeout":
        return "Timeout"
    elif is_correct:
        return "Correct"
    else:
        return "Incorrect"


def _save_to_legacy_table(
    user_id: str,
    legacy_topic: str,
    question_level: str,
    legacy_quiz_type: str,
    question_id_int: int,
    question_text: str,
    question_options_json: str,
    question_correct_answer_key: str,
    answer: str,
    result_status: str,
    score: float,
):
    """Save result to legacy user_assessment table for backward compatibility."""
    insert_user_data(
        {
            "user_id": user_id,
            "topic": legacy_topic,
            "level": question_level,
            "quiz_type": legacy_quiz_type,
            "que_id": question_id_int,
            "question": question_text,
            "options": safe_json_loads(question_options_json, {}),
            "correct_answer": question_correct_answer_key,
            "user_answer": "None" if answer.lower() == "timeout" else answer,
            "result": result_status,
            "score": score,
        }
    )


def _save_to_user_answers_table(
    session_code: str,
    question_id_int: int,
    answer: str,
    is_correct: bool,
    score: float,
    time_taken: Optional[int],
):
    """Save result to user_answers table by calling the data access layer."""
    session_details = get_session_details(session_code)
    if not session_details:
        raise ValueError(f"Session code {session_code} not found during save_result_to_db")

    internal_session_id = session_details["session_id"]

    # Use SQLAlchemy database context manager for the upsert operation
    with get_db_context() as db:
        # Call the data access function to perform the upsert
        db_upsert_user_answer(db, internal_session_id, question_id_int, answer, is_correct, score, time_taken)
        db.commit()


def save_result_to_db(
    user_id: str,
    question_id_int: int,
    answer: str,
    is_correct: bool,
    session_code: str,
    time_taken: Optional[int],
    legacy_topic: str,
    legacy_quiz_type: str,
    question_level: str,
    question_text: str,
    question_options_json: str,
    question_correct_answer_key: str,
):
    """Save quiz answer results to the database using the new schema."""
    try:
        # Determine result status
        result_status = _determine_result_status(answer, is_correct)

        # Calculate score
        score = calculate_question_score(question_level, result_status.lower())

        # Save to legacy table for backward compatibility
        _save_to_legacy_table(
            user_id,
            legacy_topic,
            question_level,
            legacy_quiz_type,
            question_id_int,
            question_text,
            question_options_json,
            question_correct_answer_key,
            answer,
            result_status,
            score,
        )

        # Save to user_answers table
        _save_to_user_answers_table(session_code, question_id_int, answer, is_correct, score, time_taken)

        debug("Answer saved successfully to database")

    except Exception as e:
        error("Error saving result", exception=e)
        raise


def _check_answer_correctness(answer: str, question: dict) -> tuple[bool, str, str]:
    """Check if answer is correct and return correctness, answer key, and answer value."""
    correct_answer_key = question["answer"]
    correct_answer_value = safe_json_loads(question["options"], {}).get(correct_answer_key, "")
    is_correct = answer.lower() == correct_answer_key.lower()

    return is_correct, correct_answer_key, correct_answer_value


def _find_question(question_topic_identifier: str, question_id: str) -> dict:
    """Find question by topic and ID, with fallback to ID-only lookup using the data access layer."""
    # First try the original method (for backward compatibility)
    question = get_questions_for_check(question_topic_identifier, question_id)

    # If not found, use the new, clean data access function for the fallback
    if not question:
        try:
            with get_db_context() as db:
                question = db_get_question_by_id(db, int(question_id))
        except (ValueError, TypeError):
            # Handle cases where question_id is not a valid integer
            question = None

    if not question:
        raise_http_exception(status_code=404, detail="Question not found")

    return question


def check_and_save_answer(
    user_id: str,
    question_id: str,
    answer: str,
    session_code: str,
    time_taken: Optional[int] = None,
):
    """
    Check the given answer and save the result to the database.
    This function is called internally by the endpoint.
    """
    try:
        # Validate session and user
        normalized_session_code, session_details, validated_user_id = validate_session_and_user(session_code, user_id)

        # Extract topic identifier from assessment name
        assessment_name = session_details["assessment_name"]
        question_topic_identifier = assessment_name.replace(" Assessment", "")

        # Find the question
        question = _find_question(question_topic_identifier, question_id)

        # Check answer correctness
        is_correct, correct_answer_key, correct_answer_value = _check_answer_correctness(answer, question)

        # Save result to database
        quiz_type = "assessment"  # Always use 'assessment' instead of 'mock' or 'final'
        save_result_to_db(
            user_id=validated_user_id,
            question_id_int=question["que_id"],
            answer=answer,
            is_correct=is_correct,
            session_code=normalized_session_code,
            time_taken=time_taken,
            legacy_topic=question_topic_identifier,
            legacy_quiz_type=quiz_type,
            question_level=question["level"],
            question_text=str(question["question"]),
            question_options_json=safe_json_dumps(question["options"]),
            question_correct_answer_key=correct_answer_key,
        )

        # Get response data
        response_data = get_session_response_data(
            session_details, is_correct, correct_answer_key, correct_answer_value, normalized_session_code
        )

        return success_response(data=response_data, message="Answer checked and saved successfully")

    except HTTPException:
        raise
    except Exception as e:
        error("Error in check_and_save_answer_internal", exception=e)
        raise HTTPException(status_code=500, detail={"error": str(e)})


@quiz_router.post("/answer")
def check_and_save_answer_endpoint(
    # Contains session_code now
    request: AnswerRequest,
    _: None = Depends(rate_limiter),
):
    """
    Checks and saves the user's answer for a specific quiz question using session_code.
    """
    # The internal check_and_save_answer function now handles fetching session details
    result = check_and_save_answer(
        user_id=request.user_id,
        question_id=request.question_id,
        answer=request.answer,
        session_code=request.session_code,
        time_taken=request.time_taken,
    )
    return result


# =============================================================================
# GET QUESTIONS API ENDPOINT AND HELPER FUNCTIONS
# =============================================================================


@quiz_router.get("/get_all_questions/{session_code_input}", response_model=AllQuestionsApiResponse, tags=["Quiz Flow"])
def get_all_questions_for_session(session_code_input: str, db: Session = Depends(get_db)):
    """
    Fetches all questions for a given assessment session.
    """
    # --- START OF DEBUG TRACE ---
    print("\n--- DEBUG TRACE: /get_all_questions ---")

    try:
        print(f"1. Received session_code_input: '{session_code_input}'")
        session_code = validate_session_code_format(session_code_input)
        print(f"2. Validated session_code: '{session_code}'")

        # This is the most critical function call. What does it return?
        session_details = get_session_and_assessment_details_by_code(session_code)
        print(f"3. Result from get_session_and_assessment_details_by_code: {session_details}")

        if not session_details:
            print("4. ERROR: Session details not found. Raising 404.")
            raise_http_exception(status_code=404, detail="Session not found.")

        assessment_id = session_details.get("assessment_id")
        print(f"4. Extracted assessment_id from session_details: {assessment_id}")

        if not assessment_id:
            print("5. FATAL ERROR: assessment_id is None or missing. Raising 500.")
            raise_http_exception(status_code=500, detail="Could not find assessment linked to this session.")

        print(f"5. Calling get_all_questions_for_assessment with assessment_id: {assessment_id}")

        # Now we call the database function with the ID we found
        all_questions = get_all_questions_for_assessment(db, assessment_id)

        if all_questions is None:
            print("6. ERROR: Database function returned None, indicating an SQLAlchemyError.")
            raise_http_exception(status_code=500, detail="A database error occurred.")

        print(f"6. Database function returned {len(all_questions)} questions.")
        print("--- DEBUG TRACE: END ---\n")

        # The final response sent to the frontend
        return {
            "questions": all_questions,
            "remaining_time_seconds": session_details.get("remaining_time_seconds", 1800),
            "attempted_answers": {},
        }

    except HTTPException:
        raise
    except Exception as e:
        error(f"UNEXPECTED EXCEPTION in get_all_questions_for_session: {e}", exc_info=True)
        raise_http_exception(status_code=500, detail="Could not retrieve assessment questions.")


@quiz_router.post("/get_next_dynamic_question", response_model=DynamicQuestionResponse, tags=["Quiz Flow"])
def get_next_dynamic_question(request: DynamicQuestionRequest, db: Session = Depends(get_db)):
    """
    Fetches the next question for a dynamic/adaptive assessment.
    """
    try:
        if not request.skill_hashes:
            raise_http_exception(status_code=400, detail="Skill hashes are required for dynamic assessments.")

        # --- NEW: Decode Skill Hashes ---
        try:
            skill_ids = [decode_skill_id(h) for h in request.skill_hashes]
            # Filter out any None values that result from invalid hashes
            skill_ids = [id for id in skill_ids if id is not None]
            if not skill_ids:
                raise ValueError("No valid skill IDs could be decoded from the provided hashes.")
        except Exception:
            raise_http_exception(status_code=400, detail="Invalid skill hashes provided.")

        # 1. Call the updated database manager function
        next_question = get_next_dynamic_question_from_db(
            db=db,
            skill_ids=skill_ids,  # Pass the decoded integer IDs
            difficulty=request.difficulty,
            exclude_ids=request.exclude_ids,
        )

        if not next_question:
            debug(f"No more dynamic questions found for skills {skill_ids} with difficulty {request.difficulty}")
            return {"question": None}

        if "answer" in next_question and next_question["answer"]:
            # This line now uses the new, correct hashing function
            hashed_answer = hash_answer(next_question["answer"])
            # We replace the plain text answer with its hash before sending
            next_question["answer"] = hashed_answer
        else:
            next_question["answer"] = None

        return {"question": next_question}

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error in get_next_dynamic_question for session {request.session_code}: {e}", exc_info=True)
        raise_http_exception(status_code=500, detail="Could not retrieve the next question.")
