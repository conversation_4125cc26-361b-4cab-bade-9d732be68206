"""
Quiz Question Generation Script using a Large Language Model (LLM).

This module orchestrates the process of generating a set of quiz questions.
It constructs a detailed prompt, queries an external LLM service, parses the
unstructured response, validates the data, and stores the final questions
in the database.
"""

import asyncio
import json
import os
import time

import yaml

# Import application-specific modules for database interaction and logging.
from ..models.quiz_manager import get_questions_by_level, insert_question_data
from ..utils.logger import debug, error, info, warning
from .llm_client import query_model


async def load_yaml_prompt(prompt):
    """
    Loads a specific prompt template from a central YAML configuration file.

    Args:
        prompt (str): The key corresponding to the desired prompt in the YAML file.

    Returns:
        The prompt string if found, otherwise an empty string.
    """
    # Dynamically construct the file path to 'config/prompt.yaml'.
    # This approach makes the script location-independent within the project structure.
    current_dir = os.path.dirname(os.path.abspath(__file__))
    app_root = os.path.dirname(current_dir)
    yaml_prompt_file = os.path.join(app_root, "config", "prompt.yaml")

    try:
        # Open and safely parse the YAML file.
        with open(yaml_prompt_file, "r", encoding="utf-8") as file:
            yaml_data = yaml.safe_load(file)
        # Navigate the parsed YAML structure to find the specific prompt.
        # The .get() methods with default values provide safe access, preventing KeyErrors.
        return yaml_data.get("prompts", {}).get(prompt, "")
    except (FileNotFoundError, yaml.YAMLError) as e:
        # Log critical errors if the prompt file is missing or malformed.
        error("Error loading YAML prompt file", exception=e)
        return ""


def add_metadata_to_questions(parsed_output, level, topic):
    """
    A helper function to enrich a list of question objects with metadata.

    This is used when questions for a single difficulty level are fetched, ensuring
    each question is tagged with its correct level and topic.
    """
    for item in parsed_output:
        item["Level"] = level
        item["Topic"] = topic
    return parsed_output


async def fetch_quiz_data(quiz_prompt, model_id, level, topic):
    """
    Queries the LLM with a prompt and processes the response to extract quiz questions.

    This function handles the entire lifecycle of an API call: sending the request,
    validating the response structure, and parsing the JSON content.

    Args:
        quiz_prompt (str): The fully formatted prompt to send to the LLM.
        model_id (str): The identifier of the LLM to use.
        level (str): The difficulty level being requested.
        topic (str): The topic for the questions to be generated.

    Returns:
        A list of question dictionaries if successful, otherwise an empty list.
    """
    start_time = time.time()

    try:
        debug(f"Fetching '{level}' level quiz data from model '{model_id}'.")
        # Await the response from the asynchronous LLM client.
        model_api_response = await query_model(quiz_prompt, model_id)

        # --- Response Validation ---
        # First, check if the client returned a structured error.
        if "error" in model_api_response:
            error_msg = model_api_response.get("message", "Unknown error from LLM client")
            error_code = model_api_response.get("error", "Unknown")
            error(f"Model API client returned an error ({error_code}): {error_msg}")
            return []

        # Validate that the response contains the expected 'choices' key.
        if "choices" not in model_api_response or not model_api_response["choices"]:
            error("Model response is missing or has an empty 'choices' array.")
            debug(f"Invalid Model Response: {model_api_response}")
            return []

        # Extract the content from the first choice in the response.
        choice = model_api_response["choices"][0]
        if "message" not in choice or "content" not in choice["message"]:
            error("Model response 'choices' object has an invalid structure.")
            debug(f"Invalid Choice Structure: {choice}")
            return []

        output_json_string = choice["message"]["content"]

        # --- JSON Extraction and Parsing ---
        # LLMs often wrap JSON in explanatory text. This finds the start of the JSON array '['
        # and the last ']' to isolate the raw JSON string.
        start_index = output_json_string.find("[")
        end_index = output_json_string.rfind("]") + 1

        if start_index == -1 or end_index == 0:
            error("Could not find a valid JSON array ('[...]') in the model's response content.")
            debug(f"Response content preview: {output_json_string[:200]}...")
            return []

        # Slice the string to get only the JSON part.
        output_json = output_json_string[start_index:end_index]

        try:
            # Attempt to parse the extracted string into a Python object.
            parsed_output = json.loads(output_json)
        except json.JSONDecodeError as json_err:
            error(f"Failed to parse JSON from model response: {json_err}")
            debug(f"Malformed JSON content preview: {output_json[:200]}...")
            return []

        # Ensure the parsed JSON is a list, as expected.
        if not isinstance(parsed_output, list):
            error(f"Parsed output from model is not a list, but type {type(parsed_output)}.")
            return []

        # --- Metadata Enrichment ---
        # If the request was for a specific level, add the metadata.
        if level != "all":
            parsed_output = add_metadata_to_questions(parsed_output, level, topic)
        else:
            # If the request was for 'all' levels, the model should provide the level.
            # We just ensure the topic is present.
            for item in parsed_output:
                if "Topic" not in item:
                    item["Topic"] = topic

        debug(
            f"Successfully fetched and parsed {len(parsed_output)} '{level}' level questions in {time.time() - start_time:.2f}s."
        )
        return parsed_output

    except (asyncio.TimeoutError, ConnectionError) as e:
        error("A network error (Timeout/Connection) occurred while fetching quiz data.", exception=e)
        return []
    except Exception as e:
        # A catch-all for any other unexpected errors during the process.
        error("An unexpected error occurred in fetch_quiz_data.", exception=e)
        return []


async def ask_for_question(quiz_name: str, topics: str, skill_id: int = None, skill_name: str = None):
    """
    The main orchestrator function to generate, categorize, and save quiz questions.

    It constructs a single, comprehensive prompt to generate questions for all difficulty
    levels at once, then processes and saves the results.

    Args:
        quiz_name (str): The name of the quiz, used as a topic fallback.
        topics (str): A string of topics for the questions.
        skill_id (int, optional): The database skill ID to associate with the questions.
        skill_name (str, optional): The skill name to use as the primary topic.

    Returns:
        The integer count of questions successfully generated and saved, or 0 on failure.
    """
    try:
        # Load required configuration from environment variables.
        model_id = os.getenv("MODEL_ID")
        if not model_id:
            error("MODEL_ID environment variable is not set. Cannot generate questions.")
            return 0

        easy_questions = int(os.getenv("EASY_QUESTIONS_COUNT", "10"))
        intermediate_questions = int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "10"))
        advanced_questions = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "10"))
        total_questions = easy_questions + intermediate_questions + advanced_questions

        debug(
            f"Target question counts: Easy={easy_questions}, Intermediate={intermediate_questions}, Advanced={advanced_questions}."
        )

        # Load the master prompt template from the YAML file.
        quiz_prompt_template = await load_yaml_prompt("questions_prompt")
        if not quiz_prompt_template:
            error("Failed to load 'questions_prompt' from YAML file. Aborting generation.")
            return 0

        # Fetch existing questions to provide as context to the LLM, helping it avoid duplicates.
        context = []
        for level in ["easy", "intermediate", "advanced"]:
            level_questions = get_questions_by_level(quiz_name, level)
            if level_questions:
                context.extend(level_questions)
        context_str = "\n".join(context)

        # Format the final prompt with all required variables.
        quiz_prompt = quiz_prompt_template.format(no=total_questions, topics=topics, level="all", context=context_str)

        info(f"Generating {total_questions} questions for skill: {skill_name or quiz_name}")
        # Make one large API call to fetch all questions at once for efficiency.
        all_questions = await fetch_quiz_data(quiz_prompt, model_id, "all", quiz_name)

        # A brief pause can help avoid rate-limiting or overloading services.
        debug("Waiting 5 seconds after LLM call before processing...")
        await asyncio.sleep(5.0)

        if not all_questions:
            error("Failed to generate questions: model returned no valid data.")
            return 0

        # --- Post-processing and Categorization ---
        questions_by_level = {"easy": [], "intermediate": [], "advanced": []}

        for question in all_questions:
            # Normalize the 'Level' field from the LLM, as it can be inconsistent.
            level = question.get("Level", "").lower()
            if level == "easy":
                questions_by_level["easy"].append(question)
            elif level == "intermediate":
                questions_by_level["intermediate"].append(question)
            elif level in ["advanced", "advance"]:  # Handle common variations.
                question["Level"] = "advanced"
                questions_by_level["advanced"].append(question)
            else:
                warning(f"Question returned with invalid level '{level}'. Defaulting to 'easy'.")
                question["Level"] = "easy"
                questions_by_level["easy"].append(question)

            # Set the topic, prioritizing the specific skill_name if available.
            question["Topic"] = skill_name if skill_name else quiz_name

        # --- Enforce Question Count Limits ---
        level_counts = {
            "easy": easy_questions,
            "intermediate": intermediate_questions,
            "advanced": advanced_questions,
        }
        all_questions_to_insert = []
        for level, target_count in level_counts.items():
            # Trim the list if the model generated too many questions for a level.
            if len(questions_by_level[level]) > target_count:
                questions_by_level[level] = questions_by_level[level][:target_count]
            # Log a warning if the model didn't generate enough questions.
            elif len(questions_by_level[level]) < target_count:
                warning(f"Model generated only {len(questions_by_level[level])}/{target_count} for level '{level}'.")

            all_questions_to_insert.extend(questions_by_level[level])

        # --- Database Insertion ---
        if all_questions_to_insert:
            insert_question_data(all_questions_to_insert, skill_id)
            questions_count = len(all_questions_to_insert)
            info(f"Saved {questions_count} questions to database for skill: {skill_name or quiz_name}")
            return questions_count
        else:
            error("No valid questions were left after processing to insert into the database.")
            # Return 0 for consistency in return type.
            return 0

    except Exception as e:
        error("A critical error occurred in the main ask_for_question function.", exception=e)
        # Ensure a 0 is always returned on any failure.
        return 0
