"""
HashID utility module for obfuscating database IDs.

This module provides a consistent way to encode integer IDs (like assessment or skill IDs)
into short, non-sequential, URL-safe strings. It also handles decoding these strings
back into their original integer IDs.

It uses a type-safe encoding mechanism to ensure that an encoded assessment ID cannot
be accidentally decoded as a skill ID.
"""

import hashlib
from typing import Optional

from hashids import Hashids

from ..config import config
from .logger import debug, warning


class HashID:
    """
    An encoder/decoder for creating obfuscated, URL-safe IDs.

    This class uses a static, deterministic salt derived from a base secret,
    ensuring that IDs can be consistently encoded and decoded across the application.
    """

    def __init__(self, base_salt: str = config.BASE_SALT, min_length: int = config.MIN_LENGTH):
        """
        Initializes the HashID instance.

        Args:
            base_salt: The base secret string used to generate the final salt.
            min_length: The minimum length of any generated hash.
        """
        self.base_salt = base_salt
        self.min_length = min_length
        # The salt is generated once and reused for efficiency.
        self.static_salt = self._generate_static_salt()

    def _generate_static_salt(self) -> str:
        """
        Generates a static, deterministic salt from the base salt.

        Although the original class was named 'Dynamic', the salt is intentionally static.
        This is critical for ensuring that an ID can be decoded correctly at any time.
        The hashing process makes the final salt non-obvious.

        Returns:
            A static salt string derived from the base salt.
        """
        return hashlib.sha256(self.base_salt.encode()).hexdigest()[: config.SALT_HASH_LENGTH]

    def _get_hashids_instance(self) -> Hashids:
        """
        Creates a configured instance of the Hashids library.

        Returns:
            A Hashids instance configured with the static salt, min length, and alphabet.
        """
        return Hashids(
            salt=self.static_salt,
            min_length=self.min_length,
            alphabet=config.ALPHABET,
        )

    def encode_id(self, entity_id: int, entity_type: str) -> str:
        """
        Encodes an entity ID into a hash string, embedding its type for safety.

        Args:
            entity_id: The integer ID to encode (e.g., 123).
            entity_type: The type of entity ('assessment', 'skill', 'session').

        Returns:
            The encoded hash string.

        Raises:
            ValueError: If the entity_id or entity_type is invalid.
        """
        # --- Input Validation ---
        if not isinstance(entity_id, int) or entity_id <= 0:
            raise ValueError(f"Invalid entity_id: {entity_id}. Must be a positive integer.")
        if entity_type not in config.ENTITY_TYPE_MAPPING:
            raise ValueError(f"Invalid entity_type: {entity_type}")

        # --- Encoding Logic ---
        try:
            hashids_instance = self._get_hashids_instance()
            # Get the unique number for the entity type (e.g., 'assessment' -> 1).
            type_number = config.ENTITY_TYPE_MAPPING[entity_type]

            # Encode both the ID and its type number together. This prevents a hash
            # for "assessment 1" from being decoded as "skill 1".
            encoded = hashids_instance.encode(entity_id, type_number)

            debug(f"Encoded {entity_type} ID {entity_id} to {encoded}")
            return encoded
        except Exception as e:
            warning(f"Error encoding {entity_type} ID {entity_id}: {str(e)}")
            # Re-raise the exception to be handled by the caller.
            raise

    def encode_assessment_id(self, assessment_id: int) -> str:
        """Convenience method to encode an assessment ID."""
        return self.encode_id(assessment_id, "assessment")

    def encode_skill_id(self, skill_id: int) -> str:
        """Convenience method to encode a skill ID."""
        return self.encode_id(skill_id, "skill")

    def encode_session_code(self, session_code: str) -> str:
        """
        Encodes a 6-digit session code string.

        This method first converts the string code to an integer before encoding.
        """
        # Validate that the session code is a string of the correct length and format.
        if not (
            isinstance(session_code, str) and len(session_code) == config.SESSION_CODE_LENGTH and session_code.isdigit()
        ):
            raise ValueError(f"Invalid session_code: {session_code}. Must be a 6-digit string.")

        # Convert the string code to an integer for encoding.
        session_code_int = int(session_code)
        return self.encode_id(session_code_int, "session")


# --- Global Instance and Decoding Functions ---


# A single, global instance to be used throughout the application.
hashid_encoder = HashID()

encode_assessment_id = hashid_encoder.encode_assessment_id
encode_skill_id = hashid_encoder.encode_skill_id
encode_session_code = hashid_encoder.encode_session_code


def _decode_hash(hash_string: str, entity_type: str) -> Optional[int]:
    """
    Internal function to decode a hash string and validate its entity type.

    Args:
        hash_string: The hash string to decode.
        entity_type: The expected type of the entity ('assessment', 'skill', 'session').

    Returns:
        The decoded integer ID if successful and type matches, otherwise None.
    """
    if not hash_string or not isinstance(hash_string, str):
        return None

    expected_type_number = config.ENTITY_TYPE_MAPPING.get(entity_type)
    if expected_type_number is None:
        return None

    try:
        hashids_instance = hashid_encoder._get_hashids_instance()
        decoded_tuple = hashids_instance.decode(hash_string)

        # A valid hash should decode to a tuple of at least two numbers: (id, type_number).
        if decoded_tuple and len(decoded_tuple) >= 2:
            entity_id, actual_type_number = decoded_tuple[0], decoded_tuple[1]

            # Check if the decoded type number matches the expected type.
            if actual_type_number == expected_type_number:
                debug(f"Successfully decoded {entity_type} hash {hash_string} to ID {entity_id}")
                return entity_id
            else:
                # The hash is valid but for a different entity type. This is not an error,
                # but a mismatched type, so we log it at debug level.
                actual_type = config.REVERSE_ENTITY_TYPE_MAPPING.get(actual_type_number, "unknown")
                debug(f"Hash {hash_string} is a valid '{actual_type}' hash, not a '{entity_type}' hash.")
                return None
    except Exception as e:
        # Log any unexpected decoding errors.
        debug(f"Failed to decode hash '{hash_string}': {str(e)}")

    warning(f"Failed to decode or validate {entity_type} hash: {hash_string}")
    return None


def decode_assessment_id(hash_string: str) -> Optional[int]:
    """Decodes a hash string expected to be an assessment ID."""
    return _decode_hash(hash_string, "assessment")


def decode_skill_id(hash_string: str) -> Optional[int]:
    """Decodes a hash string expected to be a skill ID."""
    return _decode_hash(hash_string, "skill")


def decode_session_code(hash_string: str) -> Optional[str]:
    """Decodes a session hash back to its original 6-digit string format."""
    decoded_int = _decode_hash(hash_string, "session")
    if decoded_int is not None:
        # Convert the decoded integer back to a string, padding with leading zeros if necessary.
        # e.g., 123 -> "000123"
        return str(decoded_int).zfill(config.SESSION_CODE_LENGTH)

    warning(f"Failed to decode session hash: '{hash_string}'")
    return None


def detect_hash_type(hash_string: str) -> Optional[str]:
    """
    Detects the entity type of a given hash string without returning the ID.

    Args:
        hash_string: The hash to analyze.

    Returns:
        The entity type as a string ('assessment', 'skill', 'session'), or None if not decodable.
    """
    if not hash_string or not isinstance(hash_string, str):
        return None

    try:
        hashids_instance = hashid_encoder._get_hashids_instance()
        decoded_tuple = hashids_instance.decode(hash_string)

        # Check if the decoded tuple is valid and contains a type number.
        if decoded_tuple and len(decoded_tuple) >= 2:
            type_number = decoded_tuple[1]
            # Look up the type name from the reverse mapping.
            return config.REVERSE_ENTITY_TYPE_MAPPING.get(type_number)
    except Exception:
        # Ignore exceptions, as we just want to return None on failure.
        pass

    return None


def hash_answer(answer_text: str) -> str:
    """
    Creates a secure, salted, one-way hash of an answer string.

    This is used to send a representation of the correct answer to the frontend
    without revealing the answer itself.

    Args:
        answer_text: The plain text of the correct answer option (e.g., 'a', 'b').

    Returns:
        A hex-encoded SHA-256 hash string.
    """
    if not isinstance(answer_text, str):
        # Handle cases where the answer might not be a string
        answer_text = str(answer_text)

    # Use the same base salt from your config for consistency
    salt = config.BASE_SALT

    # We combine the salt and the answer before hashing
    salted_answer = salt + answer_text

    # Create the hash using SHA-256
    hasher = hashlib.sha256(salted_answer.encode("utf-8"))

    # Return the hexadecimal representation of the hash
    return hasher.hexdigest()


def check_answer_hash(provided_answer: str, correct_answer_hash: str) -> bool:
    """
    Checks if a provided answer matches a securely hashed correct answer.

    This is used on the backend to verify a user's submitted answer against
    the hash that was previously sent.

    Args:
        provided_answer: The plain text answer submitted by the user.
        correct_answer_hash: The stored hash of the correct answer.

    Returns:
        True if the answers match, False otherwise.
    """
    # Hash the user's provided answer using the exact same method
    hash_of_provided_answer = hash_answer(provided_answer)

    # Compare the two hashes. They will only match if the original text was identical.
    return hash_of_provided_answer == correct_answer_hash
