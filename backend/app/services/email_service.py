"""
Email Service for Quiz Invitations

This module provides email functionality for sending quiz invitations
and other notifications using SMTP configuration.
"""

import os
import smtplib
from email.mime.multipart import MIME<PERSON>ultipart
from email.mime.text import MIMEText
from typing import List, Optional

from ..utils.logger import debug, error, info


class EmailService:
    """Service for sending emails via SMTP"""

    def __init__(self):
        self.smtp_host = os.getenv("SMTP_HOST")
        self.smtp_port = int(os.getenv("SMTP_PORT", 587))
        self.smtp_username = os.getenv("SMTP_USERNAME")
        self.smtp_password = os.getenv("SMTP_PASSWORD")
        self.smtp_use_tls = os.getenv("SMTP_USE_TLS", "true").lower() == "true"
        self.from_email = os.getenv("FROM_EMAIL", self.smtp_username)

        # Validate required configuration
        if not all([self.smtp_host, self.smtp_username, self.smtp_password]):
            error("SMTP configuration is incomplete. Email service will not function.")
            self.is_configured = False
        else:
            self.is_configured = True
            info(f"Email service configured with SMTP host: {self.smtp_host}")

    def send_email(
        self,
        to_emails: List[str],
        subject: str,
        body: str,
        html_body: Optional[str] = None,
    ) -> bool:
        """
        Send an email to one or more recipients

        Args:
            to_emails: List of recipient email addresses
            subject: Email subject
            body: Plain text email body
            html_body: Optional HTML email body

        Returns:
            True if email was sent successfully, False otherwise
        """
        if not self.is_configured:
            error("Email service is not properly configured")
            return False

        if not to_emails:
            error("No recipient email addresses provided")
            return False

        try:
            # Create message
            msg = MIMEMultipart("alternative")
            msg["From"] = self.from_email
            msg["To"] = ", ".join(to_emails)
            msg["Subject"] = subject

            # Add plain text body
            text_part = MIMEText(body, "plain")
            msg.attach(text_part)

            # Add HTML body if provided
            if html_body:
                html_part = MIMEText(html_body, "html")
                msg.attach(html_part)

            # Connect to SMTP server and send email
            with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                if self.smtp_use_tls:
                    server.starttls()
                
                server.login(self.smtp_username, self.smtp_password)
                server.send_message(msg)

            info(f"Email sent successfully to: {', '.join(to_emails)}")
            return True

        except Exception as e:
            error(f"Failed to send email: {str(e)}")
            return False

    def send_quiz_invitation(
        self,
        to_email: str,
        user_name: str,
        assessment_name: str,
        session_code: str,
        quiz_link: Optional[str] = None,
    ) -> bool:
        """
        Send a quiz invitation email to a user

        Args:
            to_email: Recipient email address
            user_name: Name of the user
            assessment_name: Name of the assessment
            session_code: 6-digit session code
            quiz_link: Optional direct link to the quiz

        Returns:
            True if email was sent successfully, False otherwise
        """
        if not self.is_configured:
            error("Email service is not properly configured")
            return False

        subject = f"Quiz Invitation: {assessment_name}"

        # Plain text body
        body = f"""
Hello {user_name},

You have been invited to take an assessment: {assessment_name}

Your session code is: {session_code}

To start the quiz:
1. Visit the quiz platform
2. Enter your session code: {session_code}
3. Follow the instructions to complete the assessment

{f"Or click this direct link: {quiz_link}" if quiz_link else ""}

Please complete the assessment at your earliest convenience.

Best regards,
Assessment Team
"""

        # HTML body
        html_body = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz Invitation</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }}
        .header {{
            background-color: #4F46E5;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }}
        .content {{
            background-color: #f9f9f9;
            padding: 30px;
            border-radius: 0 0 8px 8px;
            border: 1px solid #e1e1e1;
        }}
        .session-code {{
            background-color: #4F46E5;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            letter-spacing: 2px;
            border-radius: 6px;
            margin: 20px 0;
        }}
        .button {{
            background-color: #4F46E5;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 6px;
            display: inline-block;
            margin: 15px 0;
        }}
        .instructions {{
            background-color: #e0e7ff;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }}
        .footer {{
            text-align: center;
            margin-top: 20px;
            color: #666;
            font-size: 14px;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Quiz Invitation</h1>
    </div>
    <div class="content">
        <p>Hello <strong>{user_name}</strong>,</p>
        
        <p>You have been invited to take an assessment: <strong>{assessment_name}</strong></p>
        
        <div class="session-code">
            {session_code}
        </div>
        
        <div class="instructions">
            <h3>To start the quiz:</h3>
            <ol>
                <li>Visit the quiz platform</li>
                <li>Enter your session code: <strong>{session_code}</strong></li>
                <li>Follow the instructions to complete the assessment</li>
            </ol>
        </div>
        
        {f'<p><a href="{quiz_link}" class="button">Start Quiz Now</a></p>' if quiz_link else ""}
        
        <p>Please complete the assessment at your earliest convenience.</p>
        
        <div class="footer">
            <p>Best regards,<br>Assessment Team</p>
        </div>
    </div>
</body>
</html>
"""

        debug(f"Sending quiz invitation to {to_email} for session {session_code}")
        return self.send_email([to_email], subject, body, html_body)


# Global email service instance
email_service = EmailService()
