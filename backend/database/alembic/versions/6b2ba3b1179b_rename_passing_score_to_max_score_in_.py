"""Rename passing_score to max_score in assessments

Revision ID: 6b2ba3b1179b
Revises: 62a8e5ea5595
Create Date: 2025-07-22 13:07:43.424535

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "6b2ba3b1179b"
down_revision: Union[str, None] = "62a8e5ea5595"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("assessments", sa.Column("max_score", sa.Integer(), nullable=False, server_default="70"))
    op.alter_column("assessments", "max_score", server_default=None)  # optional cleanup
    op.drop_column("assessments", "passing_score")


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "assessments",
        sa.Column("passing_score", sa.INTEGER(), server_default=sa.text("70"), autoincrement=False, nullable=False),
    )
    op.drop_column("assessments", "max_score")
    # ### end Alembic commands ###
