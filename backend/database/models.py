from sqlalchemy import (
    ARRAY,
    CHAR,
    Boolean,
    CheckConstraint,
    Column,
    DateTime,
    ForeignKey,
    Index,
    Integer,
    String,
    Text,
    UniqueConstraint,
    func,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import declarative_base, relationship

Base = declarative_base()


class Skill(Base):
    __tablename__ = "skills"

    id = Column(Integer, primary_key=True)
    name = Column(Text, unique=True, nullable=False)
    description = Column(Text)
    created_at = Column(DateTime, server_default=func.current_timestamp())
    updated_at = Column(DateTime, server_default=func.current_timestamp())

    __table_args__ = (
        CheckConstraint("LENGTH(description) >= 20", name="valid_description_length"),
        UniqueConstraint("name", name="unique_skill_name"),
    )

    questions = relationship("Question", back_populates="skill")
    assessment_skills = relationship("AssessmentSkill", back_populates="skill")


class Question(Base):
    __tablename__ = "questions"

    que_id = Column(Integer, primary_key=True)
    topic = Column(Text, nullable=False)
    level = Column(Text, nullable=False)
    question = Column(Text, nullable=False)
    options = Column(JSONB, nullable=False)
    answer = Column(Text, nullable=False)
    time = Column(DateTime, server_default=func.current_timestamp())
    topics = Column(ARRAY(Text), server_default="{}")
    skill_id = Column(Integer, ForeignKey("skills.id"), nullable=False)

    __table_args__ = (
        CheckConstraint("level IN ('easy', 'intermediate', 'advanced')", name="check_level"),
        Index("idx_questions_skill_id", "skill_id"),
        Index("idx_questions_level", "level"),
    )

    skill = relationship("Skill", back_populates="questions")
    user_assessments = relationship("UserAssessment", back_populates="question_obj")
    user_answers = relationship("UserAnswer", back_populates="question")
    assessment_questions = relationship("AssessmentQuestion", back_populates="question")


class UserAssessment(Base):
    __tablename__ = "user_assessment"

    id = Column(Integer, primary_key=True)
    user_id = Column(String(255), nullable=False)
    topic = Column(Text, nullable=False)
    level = Column(String(50), nullable=False)
    quiz_type = Column(String(50), nullable=False)
    que_id = Column(Integer, ForeignKey("questions.que_id", ondelete="CASCADE"), nullable=False)
    question = Column(Text, nullable=False)
    options = Column(JSONB, nullable=False)
    user_answer = Column(Text, nullable=False)
    correct_answer = Column(Text, nullable=False)
    result = Column(Text, CheckConstraint("result IN ('Correct', 'Incorrect', 'Timeout')"))
    score = Column(Integer, nullable=False)
    time = Column(DateTime, server_default=func.current_timestamp())

    question_obj = relationship("Question", back_populates="user_assessments")


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True)
    external_id = Column(Text, unique=True, nullable=False)
    email = Column(Text)
    display_name = Column(Text)
    created_at = Column(DateTime, server_default=func.current_timestamp())
    updated_at = Column(DateTime, server_default=func.current_timestamp())

    sessions = relationship("Session", back_populates="user")


class Assessment(Base):
    __tablename__ = "assessments"

    id = Column(Integer, primary_key=True)
    name = Column(Text, nullable=False)
    description = Column(Text)
    is_final = Column(Boolean, default=False)
    created_at = Column(DateTime, server_default=func.current_timestamp())
    updated_at = Column(DateTime, server_default=func.current_timestamp())
    duration_minutes = Column(Integer, nullable=False, default=60)
    total_questions = Column(Integer, nullable=False, default=10)
    composition = Column(JSONB, default=lambda: {"basic": 6, "intermediate": 6, "advanced": 8})
    max_score = Column(Integer, nullable=False, default=70)
    question_selection_mode = Column(String(10), nullable=False, default="dynamic")

    __table_args__ = (CheckConstraint("question_selection_mode IN ('fixed', 'dynamic')", name="check_selection_mode"),)

    assessment_skills = relationship("AssessmentSkill", back_populates="assessment")
    sessions = relationship("Session", back_populates="assessment")
    quiz_logs = relationship("QuizCreationLog", back_populates="assessment")
    assessment_questions = relationship("AssessmentQuestion", back_populates="assessment")


class AssessmentSkill(Base):
    __tablename__ = "assessment_skills"

    id = Column(Integer, primary_key=True)
    assessment_id = Column(Integer, ForeignKey("assessments.id", ondelete="CASCADE"), nullable=False)
    skill_id = Column(Integer, ForeignKey("skills.id", ondelete="CASCADE"), nullable=False)
    created_at = Column(DateTime, server_default=func.current_timestamp())

    __table_args__ = (
        UniqueConstraint("assessment_id", "skill_id", name="uq_assessment_skill"),
        Index("idx_assessment_skills_skill_id", "skill_id"),
    )

    assessment = relationship("Assessment", back_populates="assessment_skills")
    skill = relationship("Skill", back_populates="assessment_skills")


class Session(Base):
    __tablename__ = "sessions"

    id = Column(Integer, primary_key=True)
    code = Column(CHAR(6), unique=True, nullable=False)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    assessment_id = Column(Integer, ForeignKey("assessments.id", ondelete="CASCADE"), nullable=False)
    status = Column(
        Text, CheckConstraint("status IN ('pending', 'in_progress', 'completed', 'expired')"), nullable=False
    )
    score = Column(Integer)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    created_at = Column(DateTime, server_default=func.current_timestamp())
    updated_at = Column(DateTime, server_default=func.current_timestamp())

    __table_args__ = (
        Index("idx_sessions_code", "code"),
        Index("idx_sessions_user_id", "user_id"),
        Index("idx_sessions_assessment_id", "assessment_id"),
    )

    user = relationship("User", back_populates="sessions")
    assessment = relationship("Assessment", back_populates="sessions")
    user_answers = relationship("UserAnswer", back_populates="session")


class UserAnswer(Base):
    __tablename__ = "user_answers"

    id = Column(Integer, primary_key=True)
    session_id = Column(Integer, ForeignKey("sessions.id", ondelete="CASCADE"), nullable=False)
    question_id = Column(Integer, ForeignKey("questions.que_id", ondelete="CASCADE"), nullable=False)
    user_answer = Column(Text)
    is_correct = Column(Boolean, nullable=False)
    score = Column(Integer, nullable=False)
    time_taken = Column(Integer)
    created_at = Column(DateTime, server_default=func.current_timestamp())

    __table_args__ = (
        UniqueConstraint("session_id", "question_id", name="uq_session_question"),
        Index("idx_user_answers_session_id", "session_id"),
        Index("idx_user_answers_question_id", "question_id"),
    )

    session = relationship("Session", back_populates="user_answers")
    question = relationship("Question", back_populates="user_answers")


class AssessmentQuestion(Base):
    __tablename__ = "assessment_questions"

    id = Column(Integer, primary_key=True)
    assessment_id = Column(Integer, ForeignKey("assessments.id", ondelete="CASCADE"), nullable=False)
    question_id = Column(Integer, ForeignKey("questions.que_id", ondelete="CASCADE"), nullable=False)
    created_at = Column(DateTime, server_default=func.current_timestamp())

    __table_args__ = (
        UniqueConstraint("assessment_id", "question_id", name="uq_assessment_question"),
        Index("idx_assessment_questions_assessment_id", "assessment_id"),
        Index("idx_assessment_questions_question_id", "question_id"),
    )

    assessment = relationship("Assessment", back_populates="assessment_questions")
    question = relationship("Question", back_populates="assessment_questions")


class QuizCreationLog(Base):
    __tablename__ = "quiz_creation_logs"

    id = Column(Integer, primary_key=True)
    user_id = Column(String(255), nullable=False)
    assessment_name = Column(String(255), nullable=False)
    assessment_description = Column(String(255), nullable=False)
    total_questions = Column(Integer, nullable=False)
    time = Column(DateTime, server_default=func.current_timestamp())
    assessment_id = Column(Integer, ForeignKey("assessments.id", ondelete="CASCADE"))

    __table_args__ = (Index("idx_quiz_creation_logs_assessment_id", "assessment_id"),)

    assessment = relationship("Assessment", back_populates="quiz_logs")
