<template>
  <div>
    <div
      class="min-h-screen bg-gradient-to-br from-phantom-dark via-phantom-dark-blue to-black font-sans relative overflow-hidden">
      <!-- Phantom Background Effects -->
      <div class="absolute inset-0 overflow-hidden">
        <!-- Grid <PERSON>tern -->
        <div class="absolute inset-0 bg-grid-phantom opacity-20" />

        <!-- Floating Particles -->
        <div
v-for="i in 30" :key="i" class="absolute rounded-full" :class="[
          i % 3 === 0 ? 'bg-phantom-blue/20 animate-float-slow' : '',
          i % 3 === 1 ? 'bg-phantom-indigo/20 animate-float-slow-reverse' : '',
          i % 3 === 2 ? 'bg-phantom-purple/20 animate-pulse-slow' : '',
        ]" :style="{
          width: `${Math.random() * 8 + 3}px`,
          height: `${Math.random() * 8 + 3}px`,
          top: `${Math.random() * 100}%`,
          left: `${Math.random() * 100}%`,
          animationDelay: `${Math.random() * 5}s`,
        }" />

        <!-- Radial Vignette -->
        <div class="absolute inset-0 bg-radial-vignette" />
      </div>

      <!-- Main Content -->
      <div class="relative z-10 min-h-screen flex items-center justify-center -mt-24">
        <div class="w-full max-w-6xl">

          <!-- Header (Stays outside the main v-if/v-else chain) -->
          <div class="text-center mb-8">
            <h2 class="text-xl font-bold text-white">
              {{ assessmentName }}
            </h2>
          </div>

          <div v-if="!quizStarted" class="card-phantom p-10 pb-16 shadow-glow-md">
            <h2
              class="text-2xl font-bold text-white mb-6 text-center bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent">
              Enter Your Details
            </h2>

            <!-- Fullscreen Mode Warning -->
            <div class="mb-6 p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
              <div class="flex items-start">
                <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path
fill-rule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clip-rule="evenodd" />
                </svg>
                <div>
                  <h3 class="text-yellow-400 font-semibold mb-2">
                    Important: Fullscreen Quiz Mode
                  </h3>
                  <ul class="text-yellow-300 text-sm space-y-1">
                    <li>
                      • The quiz will run in fullscreen mode to prevent
                      distractions
                    </li>
                    <li>
                      • Attempting to exit, switch tabs, or use shortcuts will be
                      monitored
                    </li>
                    <li>
                      • Any attempt to exit will immediately submit your quiz
                    </li>
                    <li>
                      • Ensure you're ready to complete the entire quiz before
                      starting
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <form class="space-y-6" @submit.prevent="startQuiz">
              <!-- Session Code Field -->
              <div>
                <label for="sessionCode" class="block text-sm font-medium text-white/80 mb-2">
                  Session Code (Optional)
                </label>
                <input
id="sessionCode" v-model="existingSessionCode" name="sessionCode" type="text" maxlength="6"
                  pattern="[0-9]{6}" autocomplete="off"
                  class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 font-mono text-center text-lg tracking-widest placeholder-white/40"
                  placeholder="123456" @input="onSessionCodeChange" />
                <p class="text-xs text-white/60 mt-1">
                  If you have an existing session code, enter it here. Your
                  username will be automatically filled.
                </p>
              </div>

              <!-- Username Field -->
              <div>
                <label for="username" class="block text-sm font-medium text-white/80 mb-2">Username</label>
                <input
id="username" v-model="username" name="username" type="text" autocomplete="username"
                  :required="!existingSessionCode" :disabled="isSessionCodeValid"
                  class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 disabled:opacity-50 disabled:cursor-not-allowed placeholder-white/40"
                  placeholder="Enter your username" />
                <p v-if="isSessionCodeValid" class="text-xs text-green-400 mt-1">
                  Username automatically filled from session code
                </p>
              </div>

              <!-- Email Field -->
              <div>
                <label for="email" class="block text-sm font-medium text-white/80 mb-2">Email (Optional)</label>
                <input
id="email" v-model="email" name="email" type="email" autocomplete="email"
                  class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 placeholder-white/40"
                  placeholder="Enter your email" />
              </div>

              <button
type="submit" :disabled="isLoading || (!username.trim() && !existingSessionCode)
                " class="btn-phantom w-full px-6 py-3 text-base">
                <span>{{
                  isLoading
                    ? "Starting Quiz..."
                    : existingSessionCode
                      ? "Continue with Session"
                      : "Start Quiz"
                }}</span>
              </button>
            </form>

            <!-- Error Message -->
            <div v-if="errorMessage" class="mt-6 p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
              <p class="text-red-400 text-sm">
                {{ errorMessage }}
              </p>
            </div>
          </div>

          <!-- BLOCK 2: QUIZ INTERFACE -->
          <div v-else-if="!quizCompleted" class="card-phantom p-10 pb-8 shadow-glow-md quiz-content">
            <!-- Quiz Header -->
            <div class="flex justify-between items-center mb-6">
              <div class="text-white">
                <h2 class="text-xl font-bold text-white">
                  Session: <span class="text-phantom-blue font-mono">{{ sessionCode }}</span>
                </h2>
                <div v-if="fullscreenQuiz.isFullscreen.value" class="flex items-center mt-2 text-sm text-green-400">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path
fill-rule="evenodd"
                      d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z"
                      clip-rule="evenodd" />
                  </svg>
                  Fullscreen Mode Active
                </div>
              </div>
              <div class="text-right">
                <div class="text-white text-lg font-semibold">
                  <!-- DYNAMIC MODE: Just show the current question number -->
                  <span v-if="isDynamicMode">Question {{ currentQuestionIndex + 1 }}</span>
                  <!-- FIXED MODE: Show 'X of Y' -->
                  <span v-else>Question {{ currentQuestionIndex + 1 }} of {{ quizQuestions.length }}</span>
                </div>
              </div>
            </div>

            <!-- Timer -->
            <div class="mb-6 text-center">
              <div class="text-2xl font-bold text-phantom-blue">
                Time Remaining: {{ formatTime(timeRemaining) }}
              </div>
              <div class="w-full bg-white/10 rounded-full h-2 mt-2">
                <div
                  class="h-2 rounded-full transition-all duration-1000 bg-gradient-to-r from-phantom-blue to-phantom-indigo"
                  :style="{ width: `${(timeRemaining / totalQuizTime) * 100}%` }" />
              </div>
            </div>

            <!-- Question Display Area -->
            <div class="min-h-[450px]">
              <!-- A: SHOW QUESTION -->
              <div v-if="currentQuestion && quizQuestions.length > 0">
                <div class="mb-6 min-h-[60px]">
                  <h3 class="text-xl text-white font-medium leading-relaxed question-text">
                    {{ currentQuestion.question }}
                  </h3>
                </div>

                <div class="space-y-3">
                  <button
v-for="(option, key) in currentQuestion.options" :key="key"
                    :disabled="timeRemaining <= 0 || (isDynamicMode && isCurrentQuestionAnswered)"
                    class="w-full p-4 text-left bg-white/5 border border-white/10 rounded-lg text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    :class="{
                      '!bg-phantom-blue/30 !border-phantom-blue': isAnswerSelected(key),
                      'hover:border-phantom-blue/50': !(isDynamicMode && isCurrentQuestionAnswered),
                      '!cursor-not-allowed': (isDynamicMode && isCurrentQuestionAnswered)
                    }" @click="selectAnswer(key)">
                    <div class="flex items-center">
                      <span
                        class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center text-sm font-semibold mr-4 transition-colors"
                        :class="{ '!bg-phantom-blue text-white': isAnswerSelected(key) }">
                        {{ key.toUpperCase() }}
                      </span>
                      <span class="answer-option">{{ option }}</span>
                    </div>
                  </button>
                </div>


                <!-- FIXED MODE NAVIGATION -->
                <div v-if="!isDynamicMode" class="mt-8 flex justify-between items-center">
                  <button
:disabled="currentQuestionIndex === 0"
                    class="btn-phantom-secondary px-6 py-2 disabled:opacity-50"
                    @click="goToQuestion(currentQuestionIndex - 1)">
                    <span>Previous</span>
                  </button>
                  <button
v-if="currentQuestionIndex === quizQuestions.length - 1" :disabled="isSubmittingQuiz"
                    class="btn-phantom px-6 py-2" @click="confirmAndSubmitQuiz">
                    <span>{{ isSubmittingQuiz ? "Submitting..." : "Submit Quiz" }}</span>
                  </button>
                  <button
v-else :disabled="currentQuestionIndex === quizQuestions.length - 1"
                    class="btn-phantom-secondary px-6 py-2 disabled:opacity-50"
                    @click="goToQuestion(currentQuestionIndex + 1)">
                    <span>Next</span>
                  </button>
                </div>

                <!-- DYNAMIC MODE NAVIGATION -->
                <div v-else class="mt-8 flex justify-end items-center">

                  <!-- We only need ONE button now for the dynamic mode flow -->
                  <button
:disabled="!selectedAnswerForCurrentQuestion || isSubmittingAnswer || isFetchingNextQuestion"
                    class="btn-phantom px-6 py-2 disabled:opacity-50" @click="submitDynamicAnswer">

                    <!-- The text can change based on the state -->
                    <span v-if="isSubmittingAnswer">Submitting...</span>
                    <span v-else-if="isFetchingNextQuestion">Loading Next...</span>
                    <span v-else>Submit Answer</span>

                  </button>
                </div>


              </div>

              <!-- B: SHOW LOADING SPINNER -->
              <div v-else class="text-center py-24">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-phantom-blue mx-auto mb-4" />
                <!-- DYNAMIC MODE: Different loading text -->
                <p v-if="isDynamicMode" class="text-white/70">Loading the first question...</p>
                <!-- FIXED MODE: Original text -->
                <p v-else class="text-white/70">Loading assessment questions...</p>
              </div>
            </div>

            <!-- Question Navigator Panel -->
            <div class="mt-8 pt-6 border-t border-white/10">
              <div class="flex justify-between items-center mb-3">
                <h4 class="text-sm font-semibold text-white/80">Question Navigator</h4>
                <!-- DYNAMIC MODE: No "Submit All" button, only shows at the end -->
                <button
v-if="!isDynamicMode" :disabled="isSubmittingQuiz" class="btn-phantom text-sm px-4 py-1.5"
                  @click="confirmAndSubmitQuiz">
                  <span>{{ isSubmittingQuiz ? "Submitting..." : "Submit All Answers" }}</span>
                </button>
                <!-- Show a "Finish Quiz" button in dynamic mode instead -->
                <button
v-if="isDynamicMode" :disabled="isSubmittingQuiz" class="btn-phantom text-sm px-4 py-1.5"
                  @click="confirmAndSubmitQuiz">
                  <span>{{ isSubmittingQuiz ? "Submitting..." : "Finish & See Results" }}</span>
                </button>
              </div>
              <div class="flex flex-wrap gap-2">
                <button
v-for="(question, index) in quizQuestions" :key="question.que_id"
                  class="w-8 h-8 rounded-md text-sm font-bold flex items-center justify-center transition-all duration-200"
                  :class="getNavigatorClass(index)" @click="goToQuestion(index)">
                  {{ index + 1 }}
                </button>
              </div>
              <div class="flex items-center space-x-4 mt-3 text-xs text-white/60">
                <div class="flex items-center"><span
                    class="w-3 h-3 rounded-full bg-green-500/80 mr-1.5 border border-green-300/50"></span> Answered
                </div>
                <div class="flex items-center"><span
                    class="w-3 h-3 rounded-full bg-red-500/80 mr-1.5 border border-red-300/50"></span> Skipped</div>
                <div class="flex items-center"><span
                    class="w-3 h-3 rounded-full bg-gray-500/80 mr-1.5 border border-gray-300/50"></span> Unvisited</div>
                <div class="flex items-center"><span
                    class="w-3 h-3 rounded-full bg-yellow-400/80 mr-1.5 border border-yellow-200/50"></span> Current
                </div>
              </div>
            </div>
          </div>

          <!-- BLOCK 3: QUIZ RESULTS -->
          <!-- <div v-else class="card-phantom p-10 pb-16 shadow-glow-md">
            <div>
              <h2 class="text-3xl font-bold text-white mb-6 text-center">
                {{
                  timeUp
                    ? "Time's Up!"
                    : quizSubmittedOnQuit
                      ? "Quiz Submitted!"
                      : "Quiz Completed!"
                }}
              </h2>
              <div v-if="timeUp" class="mb-4 p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                <p class="text-yellow-400 text-sm">
                  ⏰ The quiz time has expired. Your answers have been
                  automatically submitted.
                </p>
              </div>
              <div v-else-if="quizSubmittedOnQuit" class="mb-4 p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                <p class="text-blue-400 text-sm">
                  📝 Your quiz session was submitted with your current progress.
                  Your score has been calculated based on the questions you
                  answered.
                </p>
              </div>

              <div class="bg-white/5 backdrop-blur-sm p-6 rounded-lg border border-white/10 mb-6">
                <div class="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div class="text-3xl font-bold text-phantom-blue">
                      {{ correctAnswers }}
                    </div>
                    <div class="text-white/70">Correct Answers</div>
                  </div>
                  <div>
                    <div class="text-3xl font-bold text-white">
                      {{ questionsAttempted }}
                    </div>
                    <div class="text-white/70">
                      Total Questions
                    </div>
                  </div>
                  <div>
                    <div class="text-3xl font-bold text-phantom-indigo">
                      {{ currentScore.toFixed(1) }}
                    </div>
                    <div class="text-white/70">Total Score</div>
                  </div>
                </div>

                <div class="mt-6 text-center">
                  <div class="text-2xl font-bold text-white mb-2">
                    Percentage:
                    {{
                      questionsAttempted > 0
                        ? Math.round((correctAnswers / questionsAttempted) * 100)
                        : 0
                    }}%
                  </div>
                  <div class="text-lg text-white/80">
                    Performance:
                    <span class="text-phantom-indigo font-semibold">{{
                      getPerformanceLevel()
                    }}</span>
                  </div>
                </div>
              </div>

              Detailed Results Button
              <div v-if="answeredQuestions.length > 0" class="mb-6">
                <button
                  class="w-full py-4 px-6 bg-white/5 hover:bg-white/10 border border-white/10 hover:border-phantom-blue/50 rounded-lg transition-all duration-200 flex items-center justify-center"
                  @click="openDetailedResultsPopup">
                  <svg
xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-phantom-blue" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path
stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  <span class="text-lg font-semibold btn-text-visible">View Detailed Results</span>
                </button>
              </div>

              Detailed Results Popup
              <div
v-if="detailedResultsModal.isOpen.value"
                class="fixed inset-0 z-50 flex items-center justify-center p-4 pt-24 bg-black/80 backdrop-blur-sm animate-fadeIn"
                @click="detailedResultsModal.handleBackdropClick">
                <div
                  class="relative w-full max-w-2xl max-h-[75vh] bg-phantom-dark border border-white/10 rounded-lg shadow-glow-lg overflow-hidden flex flex-col animate-scaleIn"
                  @click.stop>
                  Popup Header
                  <div
                    class="p-4 border-b border-white/10 flex justify-between items-center sticky top-0 bg-phantom-dark z-10 shadow-md backdrop-blur-sm">
                    <h3 class="text-xl font-bold text-white flex items-center">
                      <svg
xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-phantom-blue" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path
stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                      Detailed Results
                    </h3>
                    <button
                      class="p-2 rounded-full bg-white/5 hover:bg-white/10 text-white/80 hover:text-white focus:outline-none transition-all duration-200 border border-white/10 hover:border-phantom-blue/50"
                      aria-label="Close popup" @click="closeDetailedResultsPopup">
                      <svg
xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path
stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  Popup Content
                  <div class="overflow-y-auto p-4 flex-grow custom-scrollbar">
                    <div class="space-y-4">
                      <div
v-for="(question, index) in answeredQuestions" :key="index"
                        class="bg-white/5 backdrop-blur-sm p-4 rounded-lg border border-white/10">
                        <div class="flex justify-between items-start mb-2">
                          <h4 class="text-white font-medium">
                            {{ index + 1 }}. {{ question.question }}
                          </h4>
                          <span
class="ml-2 px-2 py-1 rounded-full text-xs font-semibold" :class="question.isCorrect
                            ? 'bg-green-400/10 text-green-300'
                            : 'bg-red-400/10 text-red-300'
                            ">
                            {{ question.isCorrect ? "Correct" : "Incorrect" }}
                          </span>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2 mt-3">
                          <div
v-for="(option, key) in question.options" :key="key"
                            class="p-3 rounded-lg border text-sm flex items-center"
                            :class="getResultOptionClass(key, question)">
                            <span
                              class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold mr-2"
                              :class="getResultOptionIconClass(key, question)">
                              {{ key.toUpperCase() }}
                            </span>
                            <span>{{ option }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="flex space-x-4 justify-center">
                <button class="btn-phantom-secondary px-6 py-3" @click="goBackToSession">
                  <span>Back to Session</span>
                </button>
                <button class="btn-phantom px-6 py-3" @click="restartQuiz">
                  <span>Take Another Quiz</span>
                </button>
              </div>
            </div>
          </div> -->


          <div v-else class="card-phantom p-10 pb-16 shadow-glow-md text-center">
            <h2 class="text-3xl font-bold text-white mb-6">
              {{
                timeUp
                  ? "Time's Up!"
                  : quizSubmittedOnQuit
                    ? "Quiz Submitted!"
                    : "Quiz Completed!"
              }}
            </h2>

            <!-- Contextual Messages -->
            <div v-if="timeUp" class="mb-4 p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
              <p class="text-yellow-400 text-sm">
                ⏰ The quiz time has expired. Your answers have been automatically submitted.
              </p>
            </div>
            <div v-else-if="quizSubmittedOnQuit" class="mb-4 p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg">
              <p class="text-blue-400 text-sm">
                📝 Your quiz session was submitted with your current progress. Your score has been calculated based on
                the questions you answered.
              </p>
            </div>

            <!-- Call to Action & Modal Trigger -->
            <div class="my-8 flex flex-col items-center justify-center">
              <p class="text-white/80 mb-4">
                Your results have been calculated. Click the button below to view your score.
              </p>
              <button class="btn-phantom-secondary px-8 py-3 text-lg" @click="isResultsModalOpen = true">
                <span>View My Score</span>
              </button>
            </div>

            <!-- Final Action Buttons -->
            <div class="flex space-x-4 justify-center mt-12 border-t border-white/10 pt-8">
              <button class="btn-phantom-secondary px-6 py-3" @click="goBackToSession">
                <span>Back to Session</span>
              </button>
              <button class="btn-phantom px-6 py-3" @click="restartQuiz">
                <span>Take Another Quiz</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Fullscreen Quit Modal -->
    <div v-if="fullscreenQuiz.showQuitModal.value" class="fullscreen-quit-modal flex items-center justify-center">
      <div class="modal-content text-center p-8 max-w-md mx-auto">
        <div class="mb-6">
          <svg class="w-20 h-20 mx-auto text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h2 class="text-3xl font-bold text-white mb-4">Quiz Session Ending</h2>
        <p class="text-xl text-white/80 mb-6">
          You attempted to leave the quiz session.
        </p>
        <p class="text-lg text-red-400 mb-8">
          Your quiz is being submitted automatically...
        </p>
        <div class="flex justify-center mb-4">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-400"></div>
        </div>
        <p class="text-sm text-white/60 mb-4">
          You will be redirected shortly...
        </p>
        <button class="text-white/60 hover:text-white text-sm underline" @click="fullscreenQuiz.closeQuitModal()">
          Close
        </button>
      </div>
    </div>

    <ScoreboardModal :is-open="isResultsModalOpen" :session-id="sessionCode" @close="isResultsModalOpen = false" />

    <ConfirmationModal
:is-open="isConfirmModalOpen" title="Submit Quiz" :message="confirmModalMessage"
      confirm-text="Submit Now" cancel-text="Go Back" @confirm="submitEntireQuiz" @close="isConfirmModalOpen = false" />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted } from "vue";
import { useRoute, useRouter, onBeforeRouteLeave } from "vue-router";
import { api } from "@/services/api";
import {
  getErrorMessage,
  logError,
  logQuizQuit,
  logQuizSubmissionError,
  confirmQuizQuit,
} from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import {
  extractResponseData,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import { debug, info, warning, error, logApiResponse } from "@/utils/logger";
import { useDetailedResultsModal, useFullscreenQuiz } from "@/composables";
import ScoreboardModal from "@/components/ui/ScoreboardModal.vue";
import ConfirmationModal from '@/components/ui/QuizEndConfirmationModal.vue';

import {
  decodeSessionCodeFromHash,
  decodeAssessmentId,
  isHashId,
} from "@/utils/hashIds";

let submissionInterval;
let fetchingInterval;

const route = useRoute();
const router = useRouter();

// Message handling
const { setErrorMessage, clearMessage } = useMessageHandler();

// Modal handling
const detailedResultsModal = useDetailedResultsModal();

// Fullscreen quiz handling
const fullscreenQuiz = useFullscreenQuiz(() => {
  if (quizStarted.value && !quizCompleted.value) {
    submitQuizOnQuit();
  }
});

// Reactive data
const assessmentId = ref(null);
const assessmentName = ref("");
const assessmentInfo = ref(null);
const username = ref("");
const email = ref("");
const isLoading = ref(false);
const quizStarted = ref(false);
const sessionCode = ref("");
const existingSessionCode = ref("");
const isSessionCodeValid = ref(false);
const sessionCodeCheckTimeout = ref(null);
const errorMessage = ref("");
const isRouteHashId = ref(false);
const isDirectStart = ref(false);

// Quiz state
const quizCompleted = ref(false);
// const currentQuestion = ref(null);
const currentQuestionIndex = ref(0);
// const selectedAnswer = ref("");
// const answerSubmitted = ref(false);
const lastAnswerCorrect = ref(false);
const timeUp = ref(false);
const quizSubmittedOnQuit = ref(false); // Track if quiz was submitted due to user quit
const correctAnswers = ref(0);
const questionsAttempted = ref(0);
const currentScore = ref(0); // Track the actual score from backend
const maxQuestions = ref(20); // Default, will be updated based on assessment type
const totalQuizTime = ref(3600); // Default 60 minutes in seconds (60 * 60)
const timeRemaining = ref(3600);
const timerInterval = ref(null);
// const currentDifficulty = ref("easy");
// const difficultyProgression = ["easy", "intermediate", "advanced"];
const allQuestions = ref([]); // Store all questions fetched from API
const answeredQuestions = ref([]); // Store questions with user answers for results
const questionsLoaded = ref(false);
const questionStartTime = ref(null); // Track when current question was displayed
const currentCorrectAnswer = ref(""); // Store the correct answer text for current question
const currentCorrectAnswerKey = ref(""); // Store the correct answer key for current question

const isResuming = ref(false); // Track if we're resuming an existing session
const lastActiveTime = ref(null); // Track when the page was last active
const isAssessmentInfoFetching = ref(false); // Flag to prevent concurrent assessment info fetches
const lastSessionValidationTime = ref(null); // Track when session was last validated
const sessionValidationData = ref(null); // Store last session validation data

// Cache for session user data to prevent duplicate API calls
const sessionUserCache = ref(null);
const sessionUserCacheTime = ref(0);
const SESSION_USER_CACHE_DURATION = 30000; // 30 seconds

// Reactive state for quiz questions
const quizQuestions = ref([]); // Holds all questions for the assessment
const userAnswers = ref({}); // Stores user answers: { [que_id]: 'a' }
const questionStates = ref({}); // Stores question status: { [que_id]: 'pending' | 'answered' | 'skipped' }
const isSubmittingQuiz = ref(false); // Loading state for final submission

// Cache for assessment data to prevent duplicate API calls
const assessmentDataCache = ref(null);
const assessmentCacheTime = ref(0);
const ASSESSMENT_CACHE_DURATION = 60000; // 60 seconds


// --- NEW/MODIFIED STATE VARIABLES ---
const isDynamicMode = ref(false); // To track the assessment mode
const currentDifficulty = ref('easy'); // For dynamic mode
const difficultyProgression = ['easy', 'intermediate', 'advanced'];
const assessmentSkills = ref([]);


// Loading states specific to dynamic mode
const isSubmittingAnswer = ref(false);
const isFetchingNextQuestion = ref(false);

// We need a way to track the selected answer for the current question before it's locked in
const selectedAnswerForCurrentQuestion = ref(null);

const isResultsModalOpen = ref(false);
const isConfirmModalOpen = ref(false);
const confirmModalMessage = ref('');


const showResults = () => {
  quizCompleted.value = true;
  isResultsModalOpen.value = true; // Open the results modal automatically
};

// Methods
// Simplified timer update from backend
const updateTimeFromBackend = (backendTime, context = "unknown") => {
  if (
    backendTime === undefined ||
    backendTime === null ||
    isNaN(Number(backendTime))
  ) {
    debug(`Invalid backend time in ${context}`, { backendTime });
    return false;
  }

  const validBackendTime = Math.max(0, Math.floor(Number(backendTime)));
  const maxTime = Math.floor(Number(totalQuizTime.value) || 3600);

  if (validBackendTime > maxTime) {
    warning(`Backend time exceeds max time in ${context}`, {
      backendTime: validBackendTime,
      maxTime,
    });
    return false;
  }

  timeRemaining.value = validBackendTime;
  debug(`Updated timer from backend in ${context}`, {
    newTime: validBackendTime,
    context,
  });
  return true;
};

const isSessionValidationNeeded = () => {
  // If we haven't validated recently (within last 30 seconds), validate again
  if (!lastSessionValidationTime.value || !sessionValidationData.value) {
    return true;
  }

  const timeSinceLastValidation = Date.now() - lastSessionValidationTime.value;
  const VALIDATION_CACHE_TIME = 30000; // 30 seconds

  return timeSinceLastValidation > VALIDATION_CACHE_TIME;
};

// Cached session user data function to prevent duplicate API calls
const getSessionUserCached = async (sessionCode) => {
  const now = Date.now();

  // Use cached data if available and recent
  if (
    sessionUserCache.value &&
    now - sessionUserCacheTime.value < SESSION_USER_CACHE_DURATION
  ) {
    debug("Using cached session user data", {
      sessionCode,
      cachedAt: sessionUserCacheTime.value,
    });
    return sessionUserCache.value;
  }

  // Fetch fresh data
  debug("Fetching fresh session user data", { sessionCode });
  const userResponse = await api.admin.getSessionUser(sessionCode);
  const userData = extractResponseData(userResponse);

  // Cache the result
  sessionUserCache.value = userData;
  sessionUserCacheTime.value = now;

  return userData;
};


// Cached assessment data function to prevent duplicate API calls
const getAssessmentCached = async (assessmentId) => {
  const now = Date.now();

  // Use cached data if available and recent
  if (
    assessmentDataCache.value &&
    now - assessmentCacheTime.value < ASSESSMENT_CACHE_DURATION
  ) {
    debug("Using cached assessment data", {
      assessmentId,
      cachedAt: assessmentCacheTime.value,
    });
    // Return the cached data directly. We assume it's already in the correct format.
    return assessmentDataCache.value;
  }

  // Fetch fresh data
  debug("Fetching fresh assessment data", { assessmentId });
  const response = await api.admin.getAssessment(assessmentId, false);


  // --- THE FIX IS HERE ---
  // 1. Extract the full response body
  const fullResponseData = extractResponseData(response);

  // 2. Check if the response has the nested 'data' structure
  if (fullResponseData && Object.prototype.hasOwnProperty.call(fullResponseData, 'data')) {
    // 3. Extract the actual assessment object from the 'data' key
    const assessmentObject = fullResponseData.data;

    // console.log("Assessment data fetched and unnested", {
    //   assessmentId,
    //   data: assessmentObject,
    // });

    // 4. Cache ONLY the inner assessment object
    assessmentDataCache.value = assessmentObject;
    assessmentCacheTime.value = now;

    // 5. Return ONLY the inner assessment object
    return assessmentObject;
  } else {
    // Fallback for responses that might not be nested
    // console.warn("Assessment data response was not in the expected { data: ... } structure", fullResponseData);
    assessmentDataCache.value = fullResponseData;
    assessmentCacheTime.value = now;
    return fullResponseData;
  }
};


const getAssessmentMode = async (assessmentId) => {
  try {
    if (!assessmentId) return null;

    debug(`Fetching assessment mode for ID: ${assessmentId}`);
    const response = await api.admin.getAssessment(assessmentId, false);
    const responseBody = extractResponseData(response);

    // This handles both nested ({ data: {...} }) and flat structures
    const assessmentObject = responseBody?.data || responseBody;

    return assessmentObject?.question_selection_mode || null;

  } catch (err) {
    logError(err, "getAssessmentMode");
    // Return null on error so the calling function can handle it
    return null;
  }
};

const validateSessionCodeCached = async (sessionCode) => {
  // Use cached data if available and recent
  if (!isSessionValidationNeeded() && sessionValidationData.value) {
    debug("Using cached session validation data", {
      sessionCode,
      cachedAt: lastSessionValidationTime.value,
    });
    return sessionValidationData.value;
  }

  // Perform fresh validation
  debug("Performing fresh session validation", { sessionCode });
  const sessionResponse = await api.quiz.validateCode({
    session_code: sessionCode,
  });
  logApiResponse(
    "POST",
    "/api/quiz/validate_code",
    sessionResponse?.status || 200,
    sessionResponse?.data,
  );

  const sessionData = extractResponseData(sessionResponse);

  // Cache the validation data
  if (sessionData) {
    sessionValidationData.value = sessionData;
    lastSessionValidationTime.value = Date.now();
  }

  return sessionData;
};

const fetchAssessmentInfo = async () => {
  try {
    // Validate assessment ID before making API call
    if (
      !assessmentId.value ||
      isNaN(assessmentId.value) ||
      assessmentId.value <= 0
    ) {
      throw new Error("Invalid assessment ID");
    }

    // Check if we already have complete assessment info to avoid duplicate calls
    if (
      assessmentInfo.value &&
      assessmentInfo.value.duration_minutes &&
      assessmentInfo.value.total_questions &&
      assessmentInfo.value.name
    ) {
      debug("Assessment info already loaded, skipping API call", {
        assessmentId: assessmentId.value,
        name: assessmentInfo.value.name,
        duration: assessmentInfo.value.duration_minutes,
        totalQuestions: assessmentInfo.value.total_questions,
      });
      return;
    }

    // Check if another fetch is already in progress
    if (isAssessmentInfoFetching.value) {
      debug(
        "Assessment info fetch already in progress, skipping duplicate call",
      );
      return;
    }

    // Set flag to prevent concurrent fetches
    isAssessmentInfoFetching.value = true;

    const data = await getAssessmentCached(assessmentId.value);

    if (data) {
      assessmentInfo.value = data;
      assessmentName.value = data.name;

      // Set max questions from assessment data
      maxQuestions.value = data.total_questions; // Use total_questions from assessment

      isDynamicMode.value = data.question_selection_mode === "dynamic";

      if (isDynamicMode.value) {
        assessmentSkills.value = data.skills || [];
      }
      // Check if this is a dynamic assessment

      // Set quiz duration from assessment (convert minutes to seconds)
      const durationMinutes = Math.max(
        1,
        Math.floor(Number(data.duration_minutes) || 60),
      );
      totalQuizTime.value = durationMinutes * 60;

      // Only reset timeRemaining if quiz hasn't started yet
      if (!quizStarted.value) {
        timeRemaining.value = totalQuizTime.value;
        debug("Setting initial time from assessment", {
          durationMinutes,
          totalSeconds: totalQuizTime.value,
        });
      }
    } else {
      throw new Error("Failed to extract assessment data from response");
    }
  } catch (error) {
    logError(error, "fetchAssessmentInfo");
    const errorInfo = extractErrorInfo(error);
    setErrorMessage(
      errorInfo.message || "Failed to load assessment information",
    );
  } finally {
    // Always reset the flag
    isAssessmentInfoFetching.value = false;
  }
};

// A simple, reliable function to START the timer countdown
const startTimer = () => {
  // 1. Clear any old timers to prevent duplicates
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
  }

  // 2. Log the starting time for debugging
  debug(`Timer starting with ${timeRemaining.value} seconds remaining.`);

  // 3. Ensure the time is a valid number
  if (isNaN(timeRemaining.value) || timeRemaining.value <= 0) {
    error("Timer cannot start: invalid timeRemaining value.", { time: timeRemaining.value });
    return; // Do not start the timer if the time is invalid
  }

  // 4. Start the actual countdown interval
  timerInterval.value = setInterval(() => {
    timeRemaining.value--; // Decrement the time

    // 5. Check if the time has run out
    if (timeRemaining.value <= 0) {
      timeUp.value = true;
      stopTimer(); // Stop the interval

      debug("Time's up! Automatically submitting quiz.");
      submitEntireQuiz({ isTimeUp: true }); // Submit the quiz
    }
  }, 1000);
};

// A simple, reliable function to STOP the timer
const stopTimer = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
    timerInterval.value = null; // Set to null to indicate it's stopped
    debug("Timer stopped.");
  }
};


const saveTimerState = () => {
  if (!sessionCode.value || timeRemaining.value <= 0) {
    return;
  }

  const timerState = {
    timeRemaining: Math.max(0, Math.floor(Number(timeRemaining.value))),
    lastActiveTime: lastActiveTime.value || Date.now(),
    sessionCode: sessionCode.value,
    savedAt: Date.now(),
  };

  try {
    localStorage.setItem(
      `quiz_timer_${sessionCode.value}`,
      JSON.stringify(timerState),
    );
  } catch (err) {
    error("Error saving timer state", { error: err });
  }
};

const clearTimerState = () => {
  if (sessionCode.value) {
    localStorage.removeItem(`quiz_timer_${sessionCode.value}`);
  }
};

// Page visibility handlers
const handleBeforeUnload = () => {
  if (quizStarted.value && !quizCompleted.value && timeRemaining.value > 0) {
    saveTimerState();
    submitQuizOnQuit();
  }
};

// Function to submit quiz when user quits
// This function now just finalizes the session.
const submitQuizOnQuit = async () => {
  if (!sessionCode.value || !username.value || quizCompleted.value) {
    return;
  }

  // Note: With this model, any unsubmitted answers will be lost on quit.
  // The quiz will be submitted with a score of 0 for any unanswered questions.

  try {
    stopTimer();
    quizSubmittedOnQuit.value = true;

    const submissionData = {
      session_code: sessionCode.value,
      user_id: username.value,
    };

    if (navigator.sendBeacon) {
      const submitData = JSON.stringify(submissionData);
      const blob = new Blob([submitData], { type: "application/json" });
      navigator.sendBeacon("/api/quiz/submit", blob);
    } else {
      await api.quiz.submit(submissionData);
    }

    // We don't wait for the result here, just transition to the results page
    completeQuiz();

  } catch (err) {
    logQuizSubmissionError(err, "quit_handler");
  }
};

const setupPageVisibilityListeners = () => {
  // Only add beforeunload listener to warn user about leaving
  window.addEventListener("beforeunload", handleBeforeUnload);
};

const removePageVisibilityListeners = () => {
  window.removeEventListener("beforeunload", handleBeforeUnload);
};

const selectAnswer = (answerKey) => {
  if (!currentQuestion.value || timeRemaining.value <= 0) return;

  // In dynamic mode, if the question is already answered, do nothing.
  if (isDynamicMode.value && isCurrentQuestionAnswered.value) {
    return;
  }

  if (isDynamicMode.value) {
    // Store the selection temporarily until submitted
    selectedAnswerForCurrentQuestion.value = answerKey;
  } else {
    // Fixed mode logic remains the same (updates userAnswers directly)
    const questionId = currentQuestion.value.que_id;
    userAnswers.value[questionId] = answerKey;
    questionStates.value[questionId] = 'answered';
  }
};


const completeQuiz = async () => {
  stopTimer();
  clearTimerState();

  // Cleanup fullscreen mode
  await fullscreenQuiz.cleanupFullscreenQuiz();

  // Submit the session to mark it as completed
  if (sessionCode.value && username.value) {
    try {
      // Log the data being sent for debugging
      const submissionData = {
        session_code: sessionCode.value,
        user_id: username.value,
      };

      info("Submitting session with data:", submissionData);

      // First, validate the session status before submitting - use cached validation
      try {
        const sessionData = await validateSessionCodeCached(sessionCode.value);

        if (sessionData) {
          info("Session validation before submission:", {
            status: sessionData.session_status,
            remaining_time: sessionData.remaining_time_seconds,
            assessment_id: sessionData.assessment_id,
          });

          // Check if session is in a valid state for submission
          if (sessionData.session_status === "completed") {
            warning("Session is already completed, skipping submission");
            quizCompleted.value = true;
            return;
          }

          if (sessionData.session_status === "expired") {
            warning("Session has expired, skipping submission");
            quizCompleted.value = true;
            return;
          }

          if (sessionData.session_status !== "in_progress") {
            warning("Session is not in progress, cannot submit", {
              current_status: sessionData.session_status,
            });
            quizCompleted.value = true;
            return;
          }
        }
      } catch (validationError) {
        error("Error validating session before submission:", validationError);
        // Continue with submission attempt anyway
      }

      const response = await api.quiz.submit(submissionData);
      // Response is logged by axios interceptor, no need for manual logging
      extractResponseData(response); // Extract for validation but don't store unused data
    } catch (error) {
      // Log the error for debugging
      error("Error submitting session:", error);
      logError(error, "submit");

      // Log detailed error information
      if (error.response) {
        error("Submit session error details:", {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
          headers: error.response.headers,
        });

        // Check if it's a specific backend error we can handle
        if (error.response.status === 400 && error.response.data?.detail) {
          warning(
            "Backend returned validation error:",
            error.response.data.detail,
          );

          // Handle specific error cases
          const errorDetail = error.response.data.detail;
          if (errorDetail.includes("Session is not in progress")) {
            info(
              "Session is not in progress, likely already completed or expired",
            );
          } else if (errorDetail.includes("User does not match session")) {
            warning(
              "User mismatch error - this should not happen in normal flow",
            );
          } else if (errorDetail.includes("Session code is required")) {
            warning("Session code missing - this indicates a frontend bug");
          }
        } else if (error.response.status === 403) {
          warning("Session submission forbidden - user mismatch");
        } else if (error.response.status === 404) {
          warning("Session not found - may have been deleted or expired");
        }
      } else if (error.request) {
        error("Submit session request error:", error.request);
      } else {
        error("Submit session general error:", error.message);
      }

      // Don't prevent quiz completion if submission fails
      // But log a user-friendly message
      info(
        "Quiz completed locally, but session submission failed. Your progress may not be saved on the server.",
      );
    }
  } else {
    // Log warning if session code or username is missing
    warning("Cannot submit session: missing session code or username", {
      sessionCode: sessionCode.value,
      username: username.value,
    });

    // If we have a session code but no username, it might be a technical issue
    if (sessionCode.value && !username.value) {
      warning(
        "Session code exists but username is missing - this may indicate a bug in the flow",
      );
    }
  }

  // Always complete the quiz to prevent user frustration
  quizCompleted.value = true;
  info("Quiz completed successfully (local completion)");
};


// Methods for detailed results display
// const getResultOptionClass = (key, question) => {
//   if (key === question.userAnswer && key === question.correctAnswerKey) {
//     return "bg-green-400/5 border-green-400/20 text-white";
//   } else if (key === question.userAnswer) {
//     return "bg-red-400/5 border-red-400/20 text-white";
//   } else if (key === question.correctAnswerKey) {
//     return "bg-green-400/5 border-green-400/20 text-white";
//   } else {
//     return "bg-white/5 border-white/10 text-white/70";
//   }
// };

// const getResultOptionIconClass = (key, question) => {
//   if (key === question.userAnswer && key === question.correctAnswerKey) {
//     return "bg-green-400 text-white";
//   } else if (key === question.userAnswer) {
//     return "bg-red-400 text-white";
//   } else if (key === question.correctAnswerKey) {
//     return "bg-green-400 text-white";
//   } else {
//     return "bg-white/10 text-white/70";
//   }
// };

// const getPerformanceLevel = () => {
//   if (questionsAttempted.value === 0) return "Fail";

//   const percentage = (correctAnswers.value / questionsAttempted.value) * 100;

//   if (percentage === 0) return "Fail";
//   if (percentage < 33) return "Basic";
//   if (percentage < 62) return "Acceptable";
//   if (percentage < 85) return "Exceed Expectation";
//   return "OUTSTANDING";
// };

// // Detailed results popup methods (using composable)
// const openDetailedResultsPopup = detailedResultsModal.open;
// const closeDetailedResultsPopup = detailedResultsModal.close;

const formatTime = (seconds) => {
  const totalSeconds = Math.max(0, Math.floor(Number(seconds) || 0));
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const secs = totalSeconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  }
  return `${minutes}:${secs.toString().padStart(2, "0")}`;
};

const goBackToSession = () => {
  router.push("/user-sessions");
};

const restartQuiz = async () => {
  // First stop any running timers
  stopTimer();

  // Cleanup fullscreen mode
  await fullscreenQuiz.cleanupFullscreenQuiz();

  // Reset quit attempts
  fullscreenQuiz.resetQuitAttempts();

  quizCompleted.value = false;
  quizStarted.value = false;
  // currentQuestion.value = null;
  currentQuestionIndex.value = 0;
  // selectedAnswer.value = "";
  // answerSubmitted.value = false;

  lastAnswerCorrect.value = false;
  timeUp.value = false;
  correctAnswers.value = 0;
  questionsAttempted.value = 0;
  currentScore.value = 0;
  quizSubmittedOnQuit.value = false;
  // currentDifficulty.value = "easy";
  sessionCode.value = "";
  existingSessionCode.value = "";
  isSessionCodeValid.value = false;
  username.value = "";
  email.value = "";
  clearMessage();
  allQuestions.value = [];
  answeredQuestions.value = [];
  questionsLoaded.value = false;
  questionStartTime.value = null;
  currentCorrectAnswer.value = "";
  currentCorrectAnswerKey.value = "";

  quizQuestions.value = [];
  userAnswers.value = {};
  questionStates.value = {};
  isSubmittingQuiz.value = false;

  // Ensure timer is reset to a valid integer value
  timeRemaining.value = Math.floor(Number(totalQuizTime.value) || 3600);

  // Reset timer state
  isResuming.value = false;
  lastActiveTime.value = null;

  // Clear cached session validation data
  lastSessionValidationTime.value = null;
  sessionValidationData.value = null;

  // Clear any timer state from localStorage
  try {
    localStorage.removeItem("quiz_timer_temp");
  } catch (err) {
    error("Error clearing temporary timer state", { error: err });
  }

  debug("Quiz state fully reset", {
    timeRemaining: timeRemaining.value,
    totalQuizTime: totalQuizTime.value,
  });
};

const checkSessionCode = async (code) => {
  if (!code || code.length !== 6) {
    isSessionCodeValid.value = false;
    username.value = "";
    return false;
  }

  try {
    // Use cached validation to avoid duplicate calls
    const sessionData = await validateSessionCodeCached(code);

    if (sessionData) {
      const sessionStatus = sessionData.session_status;

      // Check if session is completed
      if (sessionStatus === "completed") {
        setErrorMessage(
          "This session has already been completed and cannot be used again.",
        );
        isSessionCodeValid.value = false;
        username.value = "";
        return false;
      }

      // Check if session is expired
      if (sessionStatus === "expired") {
        setErrorMessage("This session has expired and cannot be used.");
        isSessionCodeValid.value = false;
        username.value = "";
        return false;
      }

      // Check if session is in a valid state (pending or in_progress)
      if (sessionStatus !== "pending" && sessionStatus !== "in_progress") {
        setErrorMessage(`Session is not available. Status: ${sessionStatus}`);
        isSessionCodeValid.value = false;
        username.value = "";
        return false;
      }

      // Now fetch the username from the session code
      const userData = await getSessionUserCached(code);

      if (userData && userData.username) {
        // Set the username from the response
        username.value = userData.username;
        isSessionCodeValid.value = true;
        clearMessage();

        // Update assessment info - check multiple possible field names
        let rawAssessmentId =
          userData.assessment_id ||
          userData.assessmentId ||
          userData.assessment_id_hash ||
          userData.assessmentIdHash;

        // Handle case where assessment ID is not available in user data
        if (!rawAssessmentId) {
          // Try to get assessment ID from the session data itself
          const sessionAssessmentId =
            sessionData.assessment_id ||
            sessionData.assessmentId ||
            sessionData.assessment_id_hash ||
            sessionData.assessmentIdHash;

          if (sessionAssessmentId) {
            rawAssessmentId = sessionAssessmentId;
          } else {
            // Continue without assessment ID - it might be set via route params
          }
        }

        // Only process assessment ID if we have one
        if (rawAssessmentId) {
          // Check if assessment_id is a hash
          if (isHashId(rawAssessmentId)) {
            // It's a hash, decode it
            try {
              const decodedId = await decodeAssessmentId(rawAssessmentId);
              if (decodedId) {
                assessmentId.value = decodedId;
              } else {
                throw new Error(
                  `Failed to decode assessment hash: ${rawAssessmentId}`,
                );
              }
            } catch (error) {
              logError(error, "decodeAssessmentIdInCheckSession");
              setErrorMessage("Failed to load assessment information.");
              return false;
            }
          } else {
            // It's a regular ID
            const parsedId = parseInt(rawAssessmentId);
            if (!isNaN(parsedId) && parsedId > 0) {
              assessmentId.value = parsedId;
            } else {
              logError(
                new Error(
                  `Invalid assessment ID from checkSessionCode: ${rawAssessmentId}`,
                ),
                "parseAssessmentIdInCheckSession",
              );
              setErrorMessage("Invalid assessment information.");
              return false;
            }
          }
        }

        assessmentName.value = userData.assessment_name;
        assessmentInfo.value = {
          id: assessmentId.value,
          name: userData.assessment_name,
          is_final: false, // Default to false, will be updated when quiz starts
        };

        // Also set the session code
        sessionCode.value = code;

        // Note: fetchAssessmentInfo will be called centrally in onMounted to avoid duplicates
        // Only call it here if assessmentId is available and we don't already have assessment info
        if (assessmentId.value && !assessmentInfo.value?.duration_minutes) {
          await fetchAssessmentInfo();
        }

        return true;
      } else {
        setErrorMessage("Could not retrieve username for this session code.");
        isSessionCodeValid.value = false;
        username.value = "";
        return false;
      }
    } else {
      setErrorMessage("Invalid session code. Please check and try again.");
      isSessionCodeValid.value = false;
      username.value = "";
      return false;
    }
  } catch (err) {
    isSessionCodeValid.value = false;
    username.value = "";

    const errorInfo = extractErrorInfo(err);
    if (errorInfo.code === 404) {
      setErrorMessage("Invalid session code. Please check and try again.");
    } else {
      setErrorMessage(
        errorInfo.message || "Error validating session code. Please try again.",
      );
    }
    return false;
  }
};

const onSessionCodeChange = () => {
  if (sessionCodeCheckTimeout.value) {
    clearTimeout(sessionCodeCheckTimeout.value);
  }

  isSessionCodeValid.value = false;
  clearMessage();

  if (existingSessionCode.value && existingSessionCode.value.length === 6) {
    debug("Session code entered, validating after delay", {
      code: existingSessionCode.value,
    });
    sessionCodeCheckTimeout.value = setTimeout(() => {
      checkSessionCode(existingSessionCode.value)
        .then((isValid) => {
          debug("Session code validation result", {
            code: existingSessionCode.value,
            isValid: isValid,
            username: username.value,
          });
        })
        .catch((err) => {
          debug("Error validating session code", { error: err });
        });
    }, 500);
  } else {
    username.value = "";
  }
};

const currentQuestion = computed(() => {
  // If questions haven't loaded yet or the index is invalid, return null.
  if (!quizQuestions.value || quizQuestions.value.length === 0 || currentQuestionIndex.value < 0) {
    return null;
  }
  // Otherwise, return the question at the current index.
  return quizQuestions.value[currentQuestionIndex.value];
});

// NEW: Fetches all questions at the start of the quiz.
// This replaces initializeQuizSession and fetchNextQuestion.
const loadFullAssessment = async () => {
  try {
    if (!sessionCode.value) {
      throw new Error("Session code is missing, cannot load assessment.");
    }

    // BACKEND REQUIREMENT: You need an endpoint that returns all questions for a session.
    const response = await api.quiz.getAllQuestions({ session_code: sessionCode.value });
    const data = extractResponseData(response);

    if (!data || !data.questions || data.questions.length === 0) {
      throw new Error("No questions found for this assessment.");
    }


    quizQuestions.value = data.questions;
    maxQuestions.value = data.questions.length; // Set total questions count

    // Initialize states for the navigator UI
    const initialStates = {};
    const initialAnswers = data.attempted_answers || {}; // If resuming, backend might send saved answers

    quizQuestions.value.forEach(q => {
      if (initialAnswers[q.que_id]) {
        initialStates[q.que_id] = 'answered';
      } else {
        initialStates[q.que_id] = 'pending';
      }
    });

    questionStates.value = initialStates;
    userAnswers.value = initialAnswers;

    // Set initial timer values from backend if provided
    if (data.remaining_time_seconds) {
      updateTimeFromBackend(data.remaining_time_seconds, "loadFullAssessment");
    }

    timeRemaining.value = data.remaining_time_seconds || 3600; // Use a default if needed
    totalQuizTime.value = data.remaining_time_seconds || 3600; // Also set the total for the progress bar

    // Start the quiz
    goToQuestion(0); // Navigate to the first question
    startTimer();

  } catch (err) {
    logError(err, "loadFullAssessment");
    setErrorMessage(getErrorMessage(err, "Failed to load the assessment."));
    completeQuiz(); // End quiz if it fails to load
  }
};

const goToQuestion = (index) => {
  if (index < 0 || index >= quizQuestions.value.length) return;

  // In dynamic mode, you can't go forward past the last fetched question
  if (isDynamicMode.value && index > quizQuestions.value.length - 1) {
    return;
  }

  // Clear temporary answer selection when navigating away
  selectedAnswerForCurrentQuestion.value = null;

  // Fixed mode logic for skipping remains the same
  if (!isDynamicMode.value) {
    const oldQuestion = currentQuestion.value;
    if (oldQuestion) {
      const oldQuestionId = oldQuestion.que_id;
      if (!userAnswers.value[oldQuestionId] && questionStates.value[oldQuestionId] !== 'answered') {
        questionStates.value[oldQuestionId] = 'skipped';
      }
    }
  }

  currentQuestionIndex.value = index;
};


const startDynamicAssessment = async () => {
  // Reset quiz state for a fresh dynamic start
  quizQuestions.value = [];
  userAnswers.value = {};
  questionStates.value = {};
  currentDifficulty.value = 'easy';

  // Fetch the very first question
  await fetchNextDynamicQuestion();
  startTimer();
};

const submitDynamicAnswer = async () => {
  // Guard against multiple clicks or no answer
  if (!selectedAnswerForCurrentQuestion.value || isSubmittingAnswer.value || isFetchingNextQuestion.value) {
    return;
  }

  isSubmittingAnswer.value = true;

  const answer = selectedAnswerForCurrentQuestion.value;
  const question = currentQuestion.value;

  try {
    const response = await api.quiz.checkAndSaveAnswer({
      session_code: sessionCode.value,
      question_id: question.que_id.toString(),
      answer: answer,
      user_id: username.value,
    });
    const answerResult = extractResponseData(response);

    if (!answerResult || typeof answerResult.is_correct === 'undefined') {
      throw new Error("Invalid response from answer check API.");
    }

    userAnswers.value[question.que_id] = answer;
    questionStates.value[question.que_id] = 'answered';
    updateDifficulty(answerResult.is_correct);

    // Update stats...
    if (answerResult.current_score) currentScore.value = answerResult.current_score;
    if (answerResult.is_correct) correctAnswers.value++;
    questionsAttempted.value++;
    debug("Dynamic answer submitted successfully.");

    await new Promise(resolve => setTimeout(resolve, 500)); // Optional UX delay

    // Call the next function and wait for it to complete.
    // It will manage its own 'isFetchingNextQuestion' state.
    await fetchNextDynamicQuestion();
    // ==============================================================

  } catch (err) {
    logError(err, "submitDynamicAnswer combined flow");
    setErrorMessage("An error occurred. Please try again.");
  } finally {
    clearInterval(submissionInterval);
    isSubmittingAnswer.value = false;
  }
};


const fetchNextDynamicQuestion = async () => {
  // This guard is still essential to prevent multiple simultaneous fetches
  if (isFetchingNextQuestion.value) {
    return;
  }

  isFetchingNextQuestion.value = true;

  selectedAnswerForCurrentQuestion.value = null;

  try {
    const skillIdHashes = assessmentSkills.value.map(skill => skill.id_hash);
    if (skillIdHashes.length === 0) {
      throw new Error("No skills found for this assessment.");
    }
    const excludedIds = quizQuestions.value.map(q => q.que_id);

    const response = await api.quiz.getNextDynamicQuestion({
      session_code: sessionCode.value,
      difficulty: currentDifficulty.value,
      exclude_ids: excludedIds,
      skill_hashes: skillIdHashes
    });

    const data = extractResponseData(response);

    if (data && data.question) {
      quizQuestions.value.push(data.question);
      currentQuestionIndex.value = quizQuestions.value.length - 1;
    } else {
      alert("No more questions available. Submitting your results.");
      confirmAndSubmitQuiz();
    }
  } catch (err) {
    logError(err, "fetchNextDynamicQuestion");
    setErrorMessage("Failed to load the next question.");
  } finally {
    clearInterval(fetchingInterval);
    isFetchingNextQuestion.value = false;
  }
};


// Updates the difficulty level for the next question
const updateDifficulty = (isCorrect) => {
  const currentIndex = difficultyProgression.indexOf(currentDifficulty.value);
  if (isCorrect) {
    // Move up in difficulty, but don't go past 'advanced'
    if (currentIndex < difficultyProgression.length - 1) {
      currentDifficulty.value = difficultyProgression[currentIndex + 1];
    }
  } else {
    // Move down in difficulty, but don't go below 'easy'
    if (currentIndex > 0) {
      currentDifficulty.value = difficultyProgression[currentIndex - 1];
    }
  }
};


// MODIFIED - This function now handles both modes
const submitEntireQuiz = async (options = {}) => {
  if (isSubmittingQuiz.value) return;
  // Stop the timer regardless of the mode
  stopTimer();

  isConfirmModalOpen.value = false;

  if (options.isTimeUp) {
    debug("Time is up! Submitting quiz automatically.");
  }

  isSubmittingQuiz.value = true;
  clearMessage();

  try {
    // --- NEW: CONDITIONAL LOGIC FOR SUBMISSION ---

    // For FIXED mode, we need to submit all the answers that are stored locally.
    if (!isDynamicMode.value) {
      debug("Submitting all answers for FIXED mode quiz.");
      const answersToSubmit = Object.entries(userAnswers.value);

      // Use Promise.all for better performance - sends requests in parallel
      const submissionPromises = answersToSubmit.map(([question_id, answer]) => {
        const answerData = {
          user_id: username.value,
          question_id: question_id.toString(),
          answer: answer,
          session_code: sessionCode.value,
          time_taken: 0,
        };
        return api.quiz.checkAndSaveAnswer(answerData);
      });

      await Promise.all(submissionPromises);
      debug("All answers for FIXED mode quiz have been saved.");
    } else {
      // For DYNAMIC mode, answers were already saved one-by-one.
      // We just log this and proceed to finalization.
      debug("DYNAMIC mode quiz: All answers are already submitted. Proceeding to finalize session.");
    }

    // --- PART 2: FINALIZE THE SESSION (Same for both modes) ---
    debug("Finalizing session to calculate final score.");
    const submissionData = {
      session_code: sessionCode.value,
      user_id: username.value,
    };

    const response = await api.quiz.submit(submissionData);
    const resultsData = extractResponseData(response);

    if (!resultsData) {
      throw new Error("Failed to get final results after submission.");
    }

    // Populate results from the finalization response
    // Your backend's /submit_session should calculate these based on what's in the DB
    correctAnswers.value = resultsData.correct_answers_count || 0;
    questionsAttempted.value = resultsData.attempted_questions_count || Object.keys(userAnswers.value).length;
    currentScore.value = resultsData.obtained_score || 0;

    // This function transitions the UI to the results screen
    completeQuiz();
    showResults();

  } catch (err) {
    logError(err, "submitEntireQuiz");
    setErrorMessage(getErrorMessage(err, "An error occurred while submitting your quiz."));
  } finally {
    isSubmittingQuiz.value = false;
  }
};


const confirmAndSubmitQuiz = () => {
  let confirmationMessage = "";

  if (isDynamicMode.value) {
    const answeredCount = Object.keys(userAnswers.value).length;
    confirmationMessage = `You have answered ${answeredCount} question(s).\n\nAre you sure you want to finish and see your results?`;
  } else {
    const answeredCount = Object.keys(userAnswers.value).length;
    const totalCount = quizQuestions.value.length;
    const unansweredCount = totalCount - answeredCount;

    confirmationMessage = `You have answered ${answeredCount} out of ${totalCount} questions.`;
    if (unansweredCount > 0) {
      confirmationMessage += `\n\nYou have ${unansweredCount} unanswered question(s).`;
    }
  }

  // Set the message for the modal and open it
  confirmModalMessage.value = confirmationMessage;
  isConfirmModalOpen.value = true;
};

const getNavigatorClass = (index) => {
  const question = quizQuestions.value[index];
  if (!question) {
    // Fallback for a question that doesn't exist (shouldn't happen)
    return 'bg-gray-700 hover:bg-gray-600';
  }

  // Get the current state of the question (e.g., 'answered', 'skipped', 'pending')
  const state = questionStates.value[question.que_id];

  // Highlight the current question with a different style
  if (currentQuestionIndex.value === index) {
    return 'bg-yellow-400 text-black scale-110 shadow-lg border-2 border-yellow-200';
  }

  // Apply styles based on the question's state
  switch (state) {
    case 'answered':
      return 'bg-green-500/80 hover:bg-green-500 text-white border border-green-300/50';
    case 'skipped':
      return 'bg-red-500/80 hover:bg-red-500 text-white border border-red-300/50';
    default: // This covers 'pending' or any other undefined state
      return 'bg-gray-500/80 hover:bg-gray-400 text-white border border-gray-300/50';
  }
};


// This will tell us if the question we are currently viewing has been answered and locked.
const isCurrentQuestionAnswered = computed(() => {

  const q = currentQuestion.value;

  // Guard against the question not being loaded yet
  if (!q || !q.que_id) {
    return false;
  }
  const isAnswered = questionStates.value[q.que_id] === 'answered';
  return isAnswered;
});

// This helps manage the state of the answer selection buttons
const isAnswerSelected = (key) => {
  if (!currentQuestion.value) return false;
  // If the question is already permanently answered, show the recorded answer
  if (isCurrentQuestionAnswered.value) {
    return userAnswers.value[currentQuestion.value.que_id] === key;
  }
  // Otherwise, show the temporary selection
  return selectedAnswerForCurrentQuestion.value === key;
};

// In your <script setup>

// In your TakeQuiz.vue <script setup>

// Replace your existing initiateQuiz with this one

const initiateQuiz = async () => {
  if (isLoading.value) return;
  isLoading.value = true;
  clearMessage();

  try {
    // --- STAGE 1: GET SESSION & ASSESSMENT ID ---
    // (This logic remains the same)
    if (!sessionCode.value) {
      if (existingSessionCode.value) {
        sessionCode.value = existingSessionCode.value;
      } else {
        if (!username.value.trim() || !assessmentId.value) {
          throw new Error("Username and Assessment ID are required.");
        }
        const response = await api.admin.createSession({
          assessment_id: assessmentId.value,
          usernames: [username.value.trim()],
        });
        const data = extractResponseData(response);
        if (data?.sessions?.[0]?.code) {
          sessionCode.value = data.sessions[0].code;
        } else {
          throw new Error("Failed to create a new session code.");
        }
      }
    }
    if (!sessionCode.value) throw new Error("Could not determine session code.");
    if (!assessmentId.value) throw new Error("Could not determine Assessment ID.");


    // =======================================================
    // STAGE 2: DETERMINE MODE & START THE QUIZ
    // =======================================================

    // 1. Use our new, simple function to determine the mode.
    const mode = await getAssessmentMode(assessmentId.value);
    if (!mode) {
      throw new Error("Could not determine the assessment mode.");
    }
    isDynamicMode.value = (mode === 'dynamic');

    // 2. Now that we know the mode, fetch all other necessary info.
    // This will populate assessmentName, totalQuizTime, skills, etc., and use the cache correctly.
    await fetchAssessmentInfo();

    // 3. Prepare the session on the backend.
    await ensureSessionIsStarted(sessionCode.value);

    // 4. Update UI and initialize fullscreen.
    quizStarted.value = true;
    await fullscreenQuiz.initializeFullscreenQuiz();

    // 5. Call the correct start function based on the mode we found.
    if (isDynamicMode.value) {
      debug("Mode is DYNAMIC. Calling startDynamicAssessment().");
      await startDynamicAssessment();
    } else {
      debug("Mode is FIXED. Calling loadFullAssessment().");
      await loadFullAssessment();
    }

  } catch (err) {
    const errorInfo = extractErrorInfo(err);
    setErrorMessage(errorInfo.message || "Failed to start the quiz.");
    quizStarted.value = false;
    sessionCode.value = '';
  } finally {
    isLoading.value = false;
  }
};
// Also, your `startQuiz` function for the button click should be just this:
const startQuiz = () => {
  initiateQuiz();
};

const ensureSessionIsStarted = async (code) => {
  try {
    // First, check the status
    const validationResponse = await api.quiz.validateCode({ session_code: code });
    const validationData = extractResponseData(validationResponse);
    const status = validationData?.data?.session_status;

    if (status === 'completed' || status === 'expired') {
      throw new Error(`This session cannot be started. Status: ${status}`);
    }

    // If it's pending, call the start endpoint
    if (status === 'pending') {
      await api.quiz.startSession({ session_code: code });
    }
    // If it's 'in_progress', we do nothing, which is correct.

  } catch (err) {

    const errorInfo = extractErrorInfo(err);
    setErrorMessage(errorInfo.message || "Error ensuring session is started.");
    throw err;
  }
};


// Process route params from URL
const processRouteParams = async () => {
  // Check if this is a direct start from UserSessions page
  isDirectStart.value = route.query.directStart === "true";
  debug("processRouteParams", {
    isDirectStart: isDirectStart.value,
    query: route.query,
  });

  // Check if we have a session code in the route
  if (route.params.sessionCode) {
    const routeSessionCode = route.params.sessionCode;

    // Check if it's a hash ID (contains letters)
    isRouteHashId.value = isHashId(routeSessionCode);

    if (isRouteHashId.value) {
      try {
        // Decode the hash to get the session code
        const decodedCode = await decodeSessionCodeFromHash(routeSessionCode);
        if (decodedCode) {
          // Set the decoded session code
          existingSessionCode.value = decodedCode;
          sessionCode.value = decodedCode;

          // Validate the session code and get assessment ID
          const isValid = await checkSessionCode(decodedCode);

          // If validation failed, clear the session code
          if (!isValid) {
            sessionCode.value = "";
            existingSessionCode.value = "";
          }
        } else {
          setErrorMessage("Invalid session code format.");
        }
      } catch (err) {
        logError(err, "decodeSessionHash");
        setErrorMessage("Failed to decode session code.");
      }
    } else {
      // It's a regular session code
      existingSessionCode.value = routeSessionCode;

      // Validate the session code and get assessment ID
      const isValid = await checkSessionCode(routeSessionCode);

      // If validation failed, clear the session code
      if (!isValid) {
        sessionCode.value = "";
        existingSessionCode.value = "";
      }
    }
  }

  // Check if we have an assessment ID in the route (for direct assessment access)
  if (route.params.assessmentId) {
    const routeAssessmentId = route.params.assessmentId;

    // Check if it's a hash ID
    if (isHashId(routeAssessmentId)) {
      try {
        // Decode the assessment hash to get the real assessment ID
        const decodedId = await decodeAssessmentId(routeAssessmentId);
        if (decodedId) {
          assessmentId.value = decodedId;
        } else {
          setErrorMessage("Invalid assessment link.");
          return;
        }
      } catch (err) {
        logError(err, "decodeAssessmentHash");
        setErrorMessage("Failed to decode assessment link.");
        return;
      }
    } else {
      // It's a regular ID
      const parsedId = parseInt(routeAssessmentId);
      if (!isNaN(parsedId) && parsedId > 0) {
        assessmentId.value = parsedId;
      } else {
        setErrorMessage("Invalid assessment ID in URL.");
        return;
      }
    }
  }
};

onMounted(async () => {
  document.body.style.overflow = "hidden";
  document.body.style.height = "100vh";
  document.body.style.position = "fixed";
  document.body.style.width = "100%";
  document.documentElement.style.overflow = "hidden"; setupPageVisibilityListeners();

  try {

    let shouldAutoStart = false; // A flag to decide if we should auto-start

    // Check for auto-fill data in localStorage
    const autoFillData = localStorage.getItem("quiz_auto_fill");
    if (autoFillData) {
      try {
        const autoFillInfo = JSON.parse(autoFillData);
        const isRecent = autoFillInfo.timestamp && (new Date().getTime() - autoFillInfo.timestamp < 5 * 60 * 1000);

        if (isRecent || !autoFillInfo.timestamp) {
          if (autoFillInfo.username) username.value = autoFillInfo.username;
          if (autoFillInfo.email) email.value = autoFillInfo.email;
          if (autoFillInfo.session_code) {
            existingSessionCode.value = autoFillInfo.session_code;
            sessionCode.value = autoFillInfo.session_code; // IMPORTANT: Set main sessionCode too
          }
          if (autoFillInfo.assessment_name) assessmentName.value = autoFillInfo.assessment_name;
          if (autoFillInfo.auto_start === true) {
            shouldAutoStart = true;
          }
        }
        localStorage.removeItem("quiz_auto_fill");
      } catch (e) { logError(e, "parseAutoFillData"); }
    }

    // Process route-specific parameters
    await processRouteParams(); // Assuming this sets assessmentId and maybe sessionCode

    if (route.query.directStart === 'true') {
      shouldAutoStart = true;
    }

    // --- Stage 3: Fetch Essential Info (if needed) ---
    if (assessmentId.value) {
      await fetchAssessmentInfo();
    }

    // --- Stage 4: THE DECISION ---
    // After gathering all info, decide what to do.

    if (shouldAutoStart && sessionCode.value) {
      debug("Auto-start conditions met. Initiating quiz...", { sessionCode: sessionCode.value });
      // Call the ONE master function to start the quiz.
      // No need to set quizStarted here, initiateQuiz will do it.
      await initiateQuiz();
    } else if (sessionCode.value) {
      // If there's a session code but no auto-start, just validate it
      // to pre-fill the username on the form.
      debug("Pre-filling form for session code:", sessionCode.value);
      checkSessionCode(sessionCode.value).catch(err => {
        debug("Non-critical error validating pre-filled session code", { error: err });
      });
    } else if (!assessmentId.value) {
      // This is an invalid state, no assessment to start.
      setErrorMessage("Invalid assessment link. Assessment ID is missing.");
    }

  } catch (err) {
    logError(err, "onMounted");
    setErrorMessage("A critical error occurred while initializing the quiz.");
  }
});

onBeforeRouteLeave((_, __, next) => {
  if (quizStarted.value && !quizCompleted.value && timeRemaining.value > 0) {
    const confirmed = confirmQuizQuit({
      questionsAttempted: questionsAttempted.value,
    });

    if (confirmed) {
      submitQuizOnQuit();
      logQuizQuit("route_leave", {
        questionsAttempted: questionsAttempted.value,
        correctAnswers: correctAnswers.value,
        finalScore: currentScore.value,
        sessionCode: sessionCode.value,
      });
      next();
    } else {
      next(false);
    }
  } else {
    next();
  }
});

onUnmounted(async () => {
  if (quizStarted.value && !quizCompleted.value && timeRemaining.value > 0) {
    submitQuizOnQuit();
    logQuizQuit("component_unmount", {
      questionsAttempted: questionsAttempted.value,
      correctAnswers: correctAnswers.value,
      finalScore: currentScore.value,
      sessionCode: sessionCode.value,
    });
  }

  // Cleanup fullscreen mode
  await fullscreenQuiz.cleanupFullscreenQuiz();

  detailedResultsModal.cleanup();
  removePageVisibilityListeners();

  if (sessionCodeCheckTimeout.value) {
    clearTimeout(sessionCodeCheckTimeout.value);
  }

  document.body.style.overflow = "";
  document.body.style.height = "";
  document.body.style.position = "";
  document.body.style.width = "";
  document.documentElement.style.overflow = "";
});
</script>

<style scoped>
/* Fullscreen quiz styles */
:fullscreen {
  background: #0a0e17 !important;
}

:-webkit-full-screen {
  background: #0a0e17 !important;
}

:-moz-full-screen {
  background: #0a0e17 !important;
}

:-ms-fullscreen {
  background: #0a0e17 !important;
}

/* Hide scrollbars in fullscreen */
:fullscreen ::-webkit-scrollbar {
  display: none;
}

:-webkit-full-screen ::-webkit-scrollbar {
  display: none;
}

/* Prevent text selection during quiz */
.quiz-content {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Allow text selection for questions and answers */
.quiz-content .question-text,
.quiz-content .answer-option {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Fullscreen quit modal styles */
.fullscreen-quit-modal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 999999 !important;
  background: rgba(0, 0, 0, 0.95) !important;
  backdrop-filter: blur(8px) !important;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/* Ensure modal content is centered and visible */
.fullscreen-quit-modal .modal-content {
  animation: scaleIn 0.3s ease-in-out;
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }

  to {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
