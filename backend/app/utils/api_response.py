"""
API Response Standardization Module

This module provides standardized response formats for the REST API.
It ensures consistency across all endpoints for success responses, error responses,
and pagination handling.
"""

from typing import Any, Dict, List, Optional

from fastapi import HTTPException, status

from .logger import debug as log_debug
from .logger import error as log_error


def success_response(
    data: Any = None,
    message: str = "Operation successful",
    status_code: int = status.HTTP_200_OK,
    meta: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    Create a standardized success response.

    Args:
        data: The main response data
        message: A human-readable success message
        status_code: HTTP status code
        meta: Additional metadata

    Returns:
        A standardized success response dictionary
    """
    response = {
        "status_code": status_code,
        "success": True,
        "message": message,
    }

    if data is not None:
        response["data"] = data

    if meta is not None:
        response["meta"] = meta

    # Log successful response
    log_debug(f"Success response: {message}", status_code=status_code)

    return response


def error_response(
    message: str = "An error occurred",
    code: int = status.HTTP_400_BAD_REQUEST,
    error_type: str = "BadRequest",
    details: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    Create a standardized error response.

    Args:
        message: A human-readable error message
        code: HTTP status code
        error_type: Type of error
        details: Additional error details

    Returns:
        A standardized error response dictionary
    """
    error = {
        "code": code,
        "type": error_type,
        "message": message,
    }

    if details is not None:
        error["details"] = details

    # Log error response
    log_error(
        f"Error response: {message}",
        status_code=code,
        error_type=error_type,
        details=details,
    )

    return {"status_code": code, "success": False, "message": message, "error": error}


def paginated_response(
    data: List[Any],
    total: int,
    limit: int,
    offset: int,
    message: str = "Data retrieved successfully",
    status_code: int = status.HTTP_200_OK,
    additional_data: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    Create a standardized paginated response.

    Args:
        data: List of items for the current page
        total: Total number of items available
        limit: Maximum number of items per page
        offset: Starting position
        message: A human-readable success message
        status_code: HTTP status code
        additional_data: Any additional data to include in the response

    Returns:
        A standardized paginated response dictionary
    """
    response = success_response(
        data=data,
        message=message,
        status_code=status_code,
        meta={
            "pagination": {
                "total": total,
                "count": len(data),
                "limit": limit,
                "offset": offset,
            }
        },
    )

    # Add any additional data
    if additional_data:
        for key, value in additional_data.items():
            if key not in response:
                response[key] = value

    return response


def raise_http_exception(
    status_code: int = status.HTTP_400_BAD_REQUEST,
    detail: str = "Bad Request",
    headers: Optional[Dict[str, str]] = None,
) -> None:
    """
    Raise a FastAPI HTTPException with standardized format.

    Args:
        status_code: HTTP status code
        detail: Error detail message
        headers: Optional HTTP headers

    Raises:
        HTTPException: FastAPI HTTP exception
    """
    error = error_response(
        message=detail,
        code=status_code,
        error_type=get_error_type(status_code),
    )

    # Log the exception being raised
    log_error(f"HTTP Exception raised: {detail}", status_code=status_code)

    raise HTTPException(
        status_code=status_code,
        detail=error,
        headers=headers,
    )


def get_error_type(status_code: int) -> str:
    """
    Get the error type name based on the HTTP status code.

    Args:
        status_code: HTTP status code

    Returns:
        Error type name
    """
    error_types = {
        400: "BadRequest",
        401: "Unauthorized",
        403: "Forbidden",
        404: "NotFound",
        409: "Conflict",
        422: "ValidationError",
        429: "TooManyRequests",
        500: "InternalServerError",
        503: "ServiceUnavailable",
    }

    return error_types.get(status_code, "UnknownError")
