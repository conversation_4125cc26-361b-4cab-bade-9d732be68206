<!-- src/components/ConfirmationModal.vue -->
<template>
    <Teleport to="body">
        <Transition name="modal-fade">
            <div v-if="isOpen"
                class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
                @click="close">
                <div class="relative w-full max-w-md bg-phantom-dark border border-white/10 rounded-lg shadow-glow-lg overflow-hidden flex flex-col animate-scaleIn"
                    @click.stop>
                    <!-- Modal Content -->
                    <div class="p-8 text-center">
                        <!-- Icon -->
                        <div class="mb-4">
                            <svg class="w-16 h-16 mx-auto text-yellow-400" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z">
                                </path>
                            </svg>
                        </div>

                        <!-- Title -->
                        <h3 class="text-2xl font-bold text-white mb-2">{{ title }}</h3>

                        <!-- Message -->
                        <p class="text-white/80 whitespace-pre-line">{{ message }}</p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="p-4 bg-white/5 border-t border-white/10 flex justify-end space-x-4">
                        <button class="btn-phantom-secondary px-6 py-2" @click="close">
                            <span>{{ cancelText }}</span>
                        </button>
                        <button class="btn-phantom-confirm px-6 py-2" @click="confirm">
                            <span>{{ confirmText }}</span>
                        </button>
                    </div>
                </div>
            </div>
        </Transition>
    </Teleport>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue';

const props = defineProps({
    isOpen: {
        type: Boolean,
        required: true,
    },
    title: {
        type: String,
        default: 'Are you sure?',
    },
    message: {
        type: String,
        required: true,
    },
    confirmText: {
        type: String,
        default: 'Confirm',
    },
    cancelText: {
        type: String,
        default: 'Cancel',
    },
});

const emit = defineEmits(['confirm', 'close']);

const confirm = () => {
    emit('confirm');
};

const close = () => {
    emit('close');
};

const handleKeydown = (e) => {
    if (props.isOpen && e.key === 'Escape') {
        close();
    }
};

onMounted(() => {
    document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown);
});
</script>

<style>
/* Add a specific button style for confirmation, if you like */
.btn-phantom-confirm {
    @apply bg-yellow-500/10 border border-yellow-500/30 text-yellow-300 hover:bg-yellow-500/20 hover:text-yellow-200 focus:ring-yellow-400/50 rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.modal-fade-enter-active,
.modal-fade-leave-active {
    transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
    opacity: 0;
}
</style>
