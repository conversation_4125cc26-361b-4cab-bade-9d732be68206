<template>
  <header
    v-if="shouldShowHeader"
    class="fixed top-0 left-0 right-0 z-50 py-4 px-2 md:px-4 bg-phantom-dark/80 backdrop-blur-sm border-b border-white/10"
  >
    <div class="w-full">
      <div class="flex items-center justify-between">
        <!-- Logo - Far Left Corner -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center space-x-2">
            <div
              class="text-2xl font-bold bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent"
            >
              HERBIT
            </div>
          </router-link>
        </div>

        <!-- Navigation - Center -->
        <nav
          class="hidden md:flex items-center space-x-8 flex-1 justify-center"
        >
          <router-link
            v-for="(item, index) in navItems"
            :key="index"
            :to="item.path"
            class="text-white/80 hover:text-white transition-colors duration-200 text-sm font-medium"
            :class="{
              'text-white underline-gradient pb-1': $route.path === item.path,
            }"
          >
            {{ item.label }}
          </router-link>
        </nav>

        <!-- Mobile Menu Button (visible on small screens) -->
        <button
          class="md:hidden text-white p-2 focus:outline-none"
          @click="toggleMobileMenu"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              v-if="mobileMenuOpen"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
            <path
              v-else
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"
            />
          </svg>
        </button>

        <!-- Auth Buttons with Avatar Dropdown -->
        <div class="hidden md:flex items-center space-x-4">
          <template v-if="isAuthenticated">
            <div class="relative" @click="toggleUserDropdown">
              <img
                :src="`https://ui-avatars.com/api/?name=${encodeURIComponent(userInfo.name || userInfo.email)}&background=9D5FFF&color=fff&rounded=true`"
                alt="Avatar"
                class="w-9 h-9 rounded-full cursor-pointer border border-white/20"
              />
              <div
                v-if="userDropdownOpen"
                class="absolute right-0 mt-2 w-52 rounded-xl shadow-xl z-50 overflow-hidden backdrop-blur-md bg-phantom-dark/90 border border-white/10 animate-fade-slide"
              >
                <div class="px-4 py-3 border-b border-white/10">
                  <p class="text-sm font-semibold text-white">
                    {{ userInfo.name || "User" }}
                  </p>
                  <p class="text-xs text-white/70 truncate">
                    {{ userInfo.email }}
                  </p>
                </div>
                <button
                  class="w-full text-left px-4 py-2 text-sm text-white hover:bg-white/10 transition"
                  @click="logout"
                >
                  Logout
                </button>
              </div>
            </div>
          </template>
        </div>


      </div>
    </div>

    <!-- Mobile Menu (slides down when toggled) -->
    <div
      v-if="mobileMenuOpen"
      class="md:hidden absolute top-full left-0 right-0 bg-phantom-dark/95 backdrop-blur-md border-b border-white/10 animate-slide-down"
    >
      <div class="px-6 py-4">
        <nav class="flex flex-col space-y-4">
          <router-link
            v-for="(item, index) in navItems"
            :key="index"
            :to="item.path"
            class="text-white/80 hover:text-white transition-colors duration-200 text-sm font-medium py-2"
            :class="{ 'text-white font-bold': $route.path === item.path }"
            @click="mobileMenuOpen = false"
          >
            {{ item.label }}
          </router-link>
        </nav>

        <!-- Mobile Auth Buttons -->
        <div class="mt-6 flex flex-col space-y-3">
          <template v-if="isAuthenticated">
            <div
              class="flex items-center space-x-2 bg-white/5 backdrop-blur-sm px-3 py-2 rounded-full border border-white/10"
            >
              <div class="w-2 h-2 rounded-full bg-green-500" />
              <div
                v-if="userInfo && (userInfo.name || userInfo.email)"
                class="relative group"
              >
                <span class="text-sm font-medium text-white">{{
                  userInfo.name || userInfo.email
                }}</span>
                <div
                  v-if="userInfo.email && userInfo.name"
                  class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50"
                >
                  {{ userInfo.email }}
                  <div
                    class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"
                  ></div>
                </div>
              </div>
              <span v-else class="text-sm text-white/80">Authenticated</span>
            </div>
            <button
              class="btn-phantom-secondary text-sm w-full py-2"
              @click="logout"
            >
              Logout
            </button>
          </template>
          <!-- <template v-else>
            <button class="btn-phantom text-sm w-full py-2" @click="login">
              <span>Login</span>
            </button>
          </template> -->
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useRoute } from "vue-router";
import { debug, info, error, logUserAction } from "@/utils/logger";
import { hasAdminAccess } from "@/utils/authHelpers";
import { api } from "@/services/api";
import { useNavigation, useToggle } from "@/composables";

const route = useRoute();

// Composables
const navigation = useNavigation();
const mobileMenu = useToggle();

// Aliases for backward compatibility
const mobileMenuOpen = mobileMenu.isToggled;
const toggleMobileMenu = mobileMenu.toggle;

// Close mobile menu when route changes
watch(
  () => route.path,
  () => {
    mobileMenuOpen.value = false;
  },
);

// Authentication state based on the presence of an access token
const isAuthenticated = ref(false);
const userInfo = ref(null);

// Check for authentication token on component mount
onMounted(() => {
  checkAuthStatus(true); // Force refresh on initial load

  // Close mobile menu when clicking outside
  document.addEventListener("click", (event) => {
    const header = document.querySelector("header");
    if (header && !header.contains(event.target) && mobileMenuOpen.value) {
      mobileMenuOpen.value = false;
    }
  });

  // Set up a listener for storage events to detect when localStorage changes
  window.addEventListener("storage", (event) => {
    if (event.key === "user_info") {
      checkAuthStatus(false); // Use cache for storage events
    }
  });

  // Listen for our custom auth state changed event
  window.addEventListener("auth-state-changed", () => {
    debug("Auth state changed event received");
    checkAuthStatus(true); // Force refresh for auth state changes
  });
});

// Only check auth status from cache when route changes (no network call)
watch(
  () => route.path,
  () => {
    checkAuthStatus(false); // Use cached data for route changes
  },
);

// Function to check if user is authenticated and fetch user info
const checkAuthStatus = async (forceRefresh = false) => {
  debug("Checking authentication status...");

  if (!forceRefresh) {
    const storedUserInfo = localStorage.getItem("user_info");
    if (storedUserInfo) {
      try {
        userInfo.value = JSON.parse(storedUserInfo);
        isAuthenticated.value = true;
        debug("User info loaded from localStorage:", {
          userInfo: userInfo.value,
        });
        return;
      } catch (e) {
        error("Error parsing stored user info:", { error: e });
      }
    }
  }

  try {
    const data = await api.getCachedUserInfo(forceRefresh);
    debug("Received user info from cache/backend:", data);

    if (data.authenticated && data.user) {
      isAuthenticated.value = true;
      userInfo.value = data.user;
      debug("User info set:", userInfo.value);
      localStorage.setItem("user_info", JSON.stringify(data.user));
    } else {
      isAuthenticated.value = false;
      userInfo.value = null;
      localStorage.removeItem("user_info");
    }
  } catch (fetchError) {
    error("Error fetching user info:", { error: fetchError });
    isAuthenticated.value = false;
    userInfo.value = null;
  }

  debug("Is authenticated:", { isAuthenticated: isAuthenticated.value });
};

// Determine if header should be shown based on current route
const shouldShowHeader = computed(() => {
  const hiddenRoutes = ["/error"];
  return !hiddenRoutes.includes(route.path);
});

const userDropdownOpen = ref(false);

const toggleUserDropdown = () => {
  userDropdownOpen.value = !userDropdownOpen.value;
};

// Close dropdown on outside click
onMounted(() => {
  document.addEventListener("click", (e) => {
    const dropdown = document.querySelector(".relative");
    if (dropdown && !dropdown.contains(e.target)) {
      userDropdownOpen.value = false;
    }
  });
});

// Navigation items - customize based on your routes
const navItems = computed(() => {
  debug("Computing nav items with user info:", { userInfo: userInfo.value });

  const items = [{ label: "Home", path: "/" }];

  // Only add additional navigation items if the user is authenticated
  if (isAuthenticated.value && userInfo.value) {
    // Check if user has admin access (either admin group OR employee admin user)
    const userHasAdminAccess = hasAdminAccess(userInfo.value);

    debug("Navigation access check:", {
      userHasAdminAccess,
      userGroups: userInfo.value.groups,
      userId: userInfo.value.sub,
      userEmail: userInfo.value.email,
    });

    if (userHasAdminAccess) {
      debug("Adding admin navigation items");
      items.push(
        { label: "Skills", path: "/list-skills" },
        { label: "Assessments", path: "/list-assessments" },
        { label: "Sessions", path: "/sessions" },
        { label: "Reports", path: "/report-generate" },
      );
    }
    // Check if user has employee group but no admin access
    else {
      const employeeGroupName =
        import.meta.env.VITE_EMPLOYEE_GROUP_NAME || "employees";
      if (
        userInfo.value.groups &&
        userInfo.value.groups.includes(employeeGroupName)
      ) {
        debug("Adding employee navigation items");
        // Add employee-specific links - Reports and Sessions only
        items.push(
          { label: "Reports", path: "/report" },
          { label: "Sessions", path: "/user-sessions" },
        );
      }
    }
  }

  debug("Final nav items:", { items });
  return items;
});

// Login function - implements the Dex authentication flow
// eslint-disable-next-line no-unused-vars
const login = () => {
  const clientId = import.meta.env.VITE_AUTH_CLIENT_ID;
  const redirectUri = import.meta.env.VITE_AUTH_CALLBACK_URL;
  const baseAuthUrl = import.meta.env.VITE_AUTH_LOGIN_URL;
  const authUrl = `${baseAuthUrl}?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=openid+email+profile`;

  window.location.href = authUrl;
};

// Logout function
const logout = async () => {
  try {
    const response = await fetch("/api/auth/logout", {
      method: "GET",
      credentials: "include",
      headers: { Accept: "application/json" },
    });

    api.clearUserInfoCache();
    localStorage.removeItem("user_info");
    isAuthenticated.value = false;
    userInfo.value = null;

    if (response.ok) {
      const data = await response.json();
      info("Logout successful:", { data });
      logUserAction("logout_success");
    }

    navigation.navigateTo("/login");
  } catch (logoutError) {
    error("Logout error:", { error: logoutError });
    logUserAction("logout_error", { error: logoutError.message });
    api.clearUserInfoCache();
    localStorage.removeItem("user_info");
    isAuthenticated.value = false;
    userInfo.value = null;
    navigation.navigateTo("/login");
  }
};
</script>

<style scoped>
/* Underline gradient for active nav items */
.underline-gradient {
  position: relative;
}

.underline-gradient::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, #3b82f6, #6366f1);
  border-radius: 2px;
}

/* Mobile menu animation */
.animate-slide-down {
  animation: slide-down 0.3s ease-out forwards;
}

@keyframes slide-down {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .mobile-nav-item {
    @apply border-b border-white/10 py-3;
  }
}

@keyframes fade-slide {
  from {
    opacity: 0;
    transform: translateY(-6px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-slide {
  animation: fade-slide 0.2s ease-in-out;
}

</style>
