"""
Assessment-related database operations for the quiz/assessment management system.

This module contains all assessment-related database functions using SQLAlchemy ORM.
"""

import datetime
import os
from typing import List, Optional, Tuple

from database.db import get_db_context
from database.models import (
    Assessment,
    AssessmentQuestion,
    AssessmentSkill,
    Question,
    QuizCreationLog,
)
from database.models import Session as SessionModel
from database.models import (
    Skill,
    User,
    UserAnswer,
)
from sqlalchemy import and_, case, desc, func
from sqlalchemy.exc import SQLAlchemyError

from ..config.config import SCORE_MAPPING
from ..utils.logger import error, log_database_error
from .db_manager import get_performance_level


def calculate_max_possible_score(composition: dict) -> int:
    """
    Calculate maximum possible score based on composition and weights.

    Args:
        composition: Dictionary with basic, intermediate, advanced question counts

    Returns:
        Maximum possible score (total score for the composition)
    """
    total_score = 0
    for level, count in composition.items():
        if level in SCORE_MAPPING:
            total_score += count * SCORE_MAPPING[level]
    return total_score


def create_assessment_with_skills(
    assessment_name: str,
    assessment_description: str,
    question_selection_mode: str,
    total_questions: int,
    duration: int,
    skill_ids: List[int],
    composition: Optional[dict] = None,
) -> Optional[int]:
    """
    Create assessment in database with skill associations and return assessment ID.

    Args:
        assessment_name: Name of the assessment
        assessment_description: Description of the assessment
        question_selection_mode: How questions are selected ('fixed' or 'dynamic')
        total_questions: Total number of questions for the assessment
        duration: Duration in minutes
        skill_ids: List of skill IDs to associate with the assessment
        composition: Optional composition dict for question difficulty distribution

    Returns:
        Assessment ID if successful, None if failed
    """
    try:
        with get_db_context() as db:
            # Use provided composition or calculate default based on total_questions
            if composition is None:
                # Calculate default composition based on total_questions
                # Distribute questions proportionally across difficulty levels
                # Default ratio: basic:intermediate:advanced = 1:1:1.2 (approximately 33%:33%:34%)
                basic_count = total_questions // 3
                intermediate_count = total_questions // 3
                advanced_count = total_questions - basic_count - intermediate_count  # Remainder goes to advanced

                composition = {"basic": basic_count, "intermediate": intermediate_count, "advanced": advanced_count}

            # Calculate the maximum possible score for this composition
            max_score = calculate_max_possible_score(composition)

            # Create the assessment
            assessment = Assessment(
                name=assessment_name,
                description=assessment_description,
                is_final=False,  # is_final is no longer relevant but kept for DB compatibility
                total_questions=total_questions,
                question_selection_mode=question_selection_mode,
                composition=composition,
                duration_minutes=duration,
                max_score=max_score,  # Set max_score to maximum possible score
            )

            db.add(assessment)
            db.flush()  # Flush to get the ID without committing

            # Create skill associations
            for skill_id in skill_ids:
                assessment_skill = AssessmentSkill(assessment_id=assessment.id, skill_id=skill_id)
                db.add(assessment_skill)

            db.commit()
            return assessment.id

    except SQLAlchemyError as e:
        log_database_error("insert", "assessments", e, assessment_name=assessment_name)
        return None


def get_assessments_paginated(limit: int, offset: int, fields: Optional[List[str]] = None) -> Tuple[List[dict], int]:
    """
    Get all assessments with pagination and optional field filtering.

    Args:
        limit: Maximum number of items per page
        offset: Starting position
        fields: Optional list of field names to include in response. If None, returns all fields.

    Returns:
        Tuple of (assessments_list, total_count)
    """
    try:
        with get_db_context() as db:
            # Get total count first
            total = db.query(Assessment).count()

            # Get paginated results
            assessments = db.query(Assessment).order_by(desc(Assessment.created_at)).limit(limit).offset(offset).all()

            # Define all available fields
            all_fields = {
                "id": lambda a: a.id,
                "name": lambda a: a.name,
                "description": lambda a: a.description,
                "is_final": lambda a: a.is_final,
                "total_questions": lambda a: a.total_questions,
                "question_selection_mode": lambda a: a.question_selection_mode,
                "composition": lambda a: a.composition,
                "duration_minutes": lambda a: a.duration_minutes,
                "created_at": lambda a: a.created_at.isoformat() if a.created_at else None,
            }

            # Determine which fields to include
            if fields:
                # Filter to only requested fields that exist
                selected_fields = {field: all_fields[field] for field in fields if field in all_fields}
            else:
                # Include all fields if none specified
                selected_fields = all_fields

            # Convert to dict format
            assessments_list = []
            for assessment in assessments:
                assessment_dict = {field: field_func(assessment) for field, field_func in selected_fields.items()}
                assessments_list.append(assessment_dict)

            return assessments_list, total

    except SQLAlchemyError as e:
        log_database_error("select", "assessments", e, limit=limit, offset=offset)
        return [], 0


def get_assessments_with_sessions() -> List[dict]:
    """
    Get only assessments that have existing sessions.

    Returns:
        List of assessments with session counts
    """
    try:
        with get_db_context() as db:
            results = (
                db.query(
                    Assessment.id,
                    Assessment.name,
                    Assessment.description,
                    Assessment.is_final,
                    Assessment.total_questions,
                    Assessment.question_selection_mode,
                    Assessment.composition,
                    Assessment.created_at,
                    func.count(SessionModel.id).label("session_count"),
                )
                .join(SessionModel, Assessment.id == SessionModel.assessment_id)
                .group_by(
                    Assessment.id,
                    Assessment.name,
                    Assessment.description,
                    Assessment.is_final,
                    Assessment.total_questions,
                    Assessment.question_selection_mode,
                    Assessment.composition,
                    Assessment.created_at,
                )
                .order_by(desc(Assessment.created_at))
                .all()
            )

            # Convert to dict format
            assessments_list = []
            for row in results:
                assessment_dict = {
                    "id": row.id,
                    "name": row.name,
                    "description": row.description,
                    "is_final": row.is_final,
                    "total_questions": row.total_questions,
                    "question_selection_mode": row.question_selection_mode,
                    "composition": row.composition,
                    "created_at": row.created_at.isoformat() if row.created_at else None,
                    "session_count": row.session_count,
                }
                assessments_list.append(assessment_dict)

            return assessments_list

    except SQLAlchemyError as e:
        log_database_error("select", "assessments", e)
        return []


def get_user_assessments_by_id(user_id: int) -> Optional[dict]:
    """
    Get all assessments taken by a specific user with their scores.

    Args:
        user_id: The user ID

    Returns:
        Dictionary with user info and assessments or None if user not found
    """
    try:
        with get_db_context() as db:
            # Get user details (excluding sensitive data)
            user = db.query(User).filter(User.id == user_id).first()

            if not user:
                return None

            user_dict = {
                "id": user.id,
                "external_id": user.external_id,
                "display_name": user.display_name or user.external_id,
            }

            # Get all sessions for this user with assessment details and scores
            sessions_query = (
                db.query(
                    SessionModel.id.label("session_id"),
                    SessionModel.created_at.label("session_created"),
                    SessionModel.completed_at.label("session_completed"),
                    Assessment.id.label("assessment_id"),
                    Assessment.name.label("assessment_name"),
                    Assessment.question_selection_mode.label("mode"),
                    Assessment.description.label("assessment_description"),
                    SessionModel.score,
                    SessionModel.status,
                    func.coalesce(func.sum(case((Question.level == "easy", 1), else_=0)), 0).label("easy_count"),
                    func.coalesce(func.sum(case((Question.level == "intermediate", 1), else_=0)), 0).label(
                        "intermediate_count"
                    ),
                    func.coalesce(func.sum(case((Question.level == "advanced", 1), else_=0)), 0).label(
                        "advanced_count"
                    ),
                )
                .join(Assessment, SessionModel.assessment_id == Assessment.id)
                .outerjoin(AssessmentQuestion, Assessment.id == AssessmentQuestion.assessment_id)
                .outerjoin(Question, AssessmentQuestion.question_id == Question.que_id)
                .filter(SessionModel.user_id == user_id)
                .group_by(
                    SessionModel.id,
                    SessionModel.created_at,
                    SessionModel.completed_at,
                    SessionModel.score,
                    SessionModel.status,
                    Assessment.id,
                    Assessment.name,
                    Assessment.question_selection_mode,
                    Assessment.description,
                )
                .order_by(desc(SessionModel.status == "completed"), desc(SessionModel.created_at))
            )

            sessions = sessions_query.all()

            # Convert to dict format
            sessions_list = []
            for session in sessions:
                session_dict = {
                    "session_id": session.session_id,
                    "session_created": session.session_created.isoformat() if session.session_created else None,
                    "session_completed": session.session_completed.isoformat() if session.session_completed else None,
                    "assessment_id": session.assessment_id,
                    "assessment_name": session.assessment_name,
                    "mode": session.mode,
                    "assessment_description": session.assessment_description,
                    "score": session.score,
                    "status": session.status,
                    "easy_count": session.easy_count,
                    "intermediate_count": session.intermediate_count,
                    "advanced_count": session.advanced_count,
                }
                sessions_list.append(session_dict)

            return {"user": user_dict, "assessments": sessions_list}

    except SQLAlchemyError as e:
        log_database_error("select", "users", e, user_id=user_id)
        return None


def get_user_assessments_by_email(email: str) -> Optional[dict]:
    """
    Get all assessments taken by a specific user identified by email.

    Args:
        email: The user's email address

    Returns:
        Dictionary with user info and assessments or None if user not found
    """
    try:
        with get_db_context() as db:
            # Get user details (excluding sensitive data from response)
            user = db.query(User).filter(User.email == email).first()

            if not user:
                return None

            # user_dict = {
            #     "id": user.id,
            #     "external_id": user.external_id,
            #     "display_name": user.display_name or user.external_id,
            # }

            # Use the existing function to get assessments
            return get_user_assessments_by_id(user.id)

    except SQLAlchemyError as e:
        log_database_error("select", "users", e, email=email)
        return None


def add_questions_to_fixed_assessment(assessment_id: int, question_ids: List[int]) -> Tuple[bool, str]:
    """
    Add questions to a fixed assessment.

    Args:
        assessment_id: The assessment ID
        question_ids: List of question IDs to add

    Returns:
        Tuple of (success: bool, message: str)
    """
    try:
        with get_db_context() as db:
            # First, validate the assessment exists and is a fixed assessment
            assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
            if not assessment:
                return False, f"Assessment with ID {assessment_id} not found"

            if assessment.question_selection_mode != "fixed":
                return False, f"Assessment with ID {assessment_id} is not a fixed assessment"

            # Clear existing questions for this assessment
            db.query(AssessmentQuestion).filter(AssessmentQuestion.assessment_id == assessment_id).delete()

            # Add the new questions
            for question_id in question_ids:
                assessment_question = AssessmentQuestion(assessment_id=assessment_id, question_id=question_id)
                db.add(assessment_question)

            db.commit()
            return True, f"Successfully added {len(question_ids)} questions to fixed assessment"

    except SQLAlchemyError as e:
        log_database_error("insert", "assessment_questions", e, assessment_id=assessment_id)
        return False, f"Error adding questions to assessment: {str(e)}"


def get_assessment_description(assessment_id):
    """
    Get the description of an assessment by its ID.

    Args:
        assessment_id (int): The ID of the assessment.

    Returns:
        str: The description of the assessment, or empty string if not found.
    """
    try:
        with get_db_context() as db:
            assessment = db.query(Assessment.description).filter(Assessment.id == assessment_id).first()

            if assessment:
                return assessment.description or ""
            return ""
    except SQLAlchemyError as e:
        log_database_error("select", "assessments", e, assessment_id=assessment_id)
        return ""


def calculate_total_score_for_assessment(assessment_id, session_id=None):
    """
    Calculate the correct total possible score for an assessment based on its mode.

    For fixed assessments: Calculate based on pre-selected questions
    For dynamic assessments: Calculate based on questions actually attempted in the session

    Args:
        assessment_id (int): The assessment ID
        session_id (int, optional): The session ID (required for dynamic assessments)

    Returns:
        int: The total possible score for this assessment
    """
    try:
        with get_db_context() as db:
            # Get assessment mode
            assessment = db.query(Assessment.question_selection_mode).filter(Assessment.id == assessment_id).first()

            if not assessment:
                return 0

            mode = assessment.question_selection_mode

            if mode == "fixed":
                # For fixed assessments, calculate based on pre-selected questions
                level_counts = (
                    db.query(Question.level, func.count().label("count"))
                    .join(AssessmentQuestion, Question.que_id == AssessmentQuestion.question_id)
                    .filter(AssessmentQuestion.assessment_id == assessment_id)
                    .group_by(Question.level)
                    .all()
                )

                total_score = 0
                for level, count in level_counts:
                    if level == "easy":
                        total_score += count * 1
                    elif level == "intermediate":
                        total_score += count * 2
                    elif level == "advanced":
                        total_score += count * 3

                return total_score

            else:  # dynamic mode
                # For dynamic assessments, calculate based on questions actually attempted
                if not session_id:
                    return 0

                level_counts = (
                    db.query(Question.level, func.count().label("count"))
                    .join(UserAnswer, Question.que_id == UserAnswer.question_id)
                    .filter(UserAnswer.session_id == session_id)
                    .group_by(Question.level)
                    .all()
                )

                total_score = 0
                for level, count in level_counts:
                    if level == "easy":
                        total_score += count * 1
                    elif level == "intermediate":
                        total_score += count * 2
                    elif level == "advanced":
                        total_score += count * 3

                return total_score

    except SQLAlchemyError as e:
        log_database_error("calculate", "user_answers", e, assessment_id=assessment_id)
        return 0


def get_performance_level_with_correct_total(obtained_score, assessment_id, session_id=None):
    """
    Calculate performance level using the correct total score based on assessment mode.

    Args:
        obtained_score (int): Score obtained by the user
        assessment_id (int): The assessment ID
        session_id (int, optional): The session ID (required for dynamic assessments)

    Returns:
        str: Performance level
    """
    total_score = calculate_total_score_for_assessment(assessment_id, session_id)
    if total_score == 0:
        return "Fail"

    return get_performance_level(obtained_score, total_score)


def get_assessment_by_id(assessment_id: int, include_questions: bool = True, include_answers: bool = False):
    """
    Get a single assessment with its questions and associated skills.

    Args:
        assessment_id: The ID of the assessment to retrieve
        include_questions: Whether to include available_questions in the response
        include_answers: Whether to include answers in the response (for security)

    Returns:
        A dictionary containing assessment details, or None if not found
    """
    try:
        with get_db_context() as db:
            # First check if the assessment exists
            assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
            if not assessment:
                return None

            assessment_dict = {
                "id": assessment.id,
                "name": assessment.name,
                "description": assessment.description,
                "is_final": assessment.is_final,
                "total_questions": assessment.total_questions,
                "question_selection_mode": assessment.question_selection_mode,
                "composition": assessment.composition,
                "duration_minutes": assessment.duration_minutes,
                "created_at": assessment.created_at,
                "updated_at": assessment.updated_at,
            }

            # Get the skill IDs associated with this assessment
            skill_associations = db.query(AssessmentSkill).filter(AssessmentSkill.assessment_id == assessment_id).all()
            skill_ids = [skill_assoc.skill_id for skill_assoc in skill_associations]
            assessment_dict["skill_ids"] = skill_ids

            # Get skill names for display
            if skill_ids:
                skills = db.query(Skill).filter(Skill.id.in_(skill_ids)).all()
                assessment_dict["skills"] = [{"id": skill.id, "name": skill.name} for skill in skills]
            else:
                assessment_dict["skills"] = []

            # Only include questions if requested
            if include_questions and skill_ids:
                # Get all questions for these skills with selection status
                questions_query = (
                    db.query(
                        Question,
                        Skill.name.label("skill_name"),
                        AssessmentQuestion.question_id.label("selected_question_id"),
                    )
                    .join(Skill, Question.skill_id == Skill.id)
                    .outerjoin(
                        AssessmentQuestion,
                        and_(
                            Question.que_id == AssessmentQuestion.question_id,
                            AssessmentQuestion.assessment_id == assessment_id,
                        ),
                    )
                    .filter(Question.skill_id.in_(skill_ids))
                    .order_by(Question.level, Question.time.desc())
                )

                questions_data = questions_query.all()
                all_questions = []
                for question_data in questions_data:
                    question = question_data.Question
                    question_dict = {
                        "que_id": question.que_id,
                        "topic": question.topic,
                        "level": question.level,
                        "question": question.question,
                        "options": question.options,
                        "time": question.time,
                        "skill_id": question.skill_id,
                        "skill_name": question_data.skill_name,
                        "selected": question_data.selected_question_id is not None,
                    }

                    # Include answer only if requested
                    if include_answers:
                        question_dict["answer"] = question.answer
                    else:
                        question_dict["answer"] = None

                    all_questions.append(question_dict)

                assessment_dict["available_questions"] = all_questions

            return assessment_dict

    except SQLAlchemyError as e:
        error(f"Error fetching assessment {assessment_id}: {str(e)}")
        return None


def get_assessment_questions_by_id(assessment_id: int):
    """
    Get all available questions for an assessment based on its associated skills.

    Args:
        assessment_id: The ID of the assessment

    Returns:
        A dictionary containing assessment questions and related information, or None if not found
    """
    try:
        with get_db_context() as db:
            # First check if the assessment exists
            assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
            if not assessment:
                return None

            # Get the skill IDs associated with this assessment
            skill_associations = db.query(AssessmentSkill).filter(AssessmentSkill.assessment_id == assessment_id).all()
            skill_ids = [skill_assoc.skill_id for skill_assoc in skill_associations]

            if not skill_ids:
                return {
                    "assessment_id": assessment_id,
                    "assessment_name": assessment.name,
                    "question_selection_mode": assessment.question_selection_mode,
                    "questions": [],
                    "counts": {
                        "easy": 0,
                        "intermediate": 0,
                        "advanced": 0,
                        "selected_easy": 0,
                        "selected_intermediate": 0,
                        "selected_advanced": 0,
                        "required_easy": int(os.getenv("EASY_QUESTIONS_COUNT", "6")),
                        "required_intermediate": int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "6")),
                        "required_advanced": int(os.getenv("ADVANCED_QUESTIONS_COUNT", "8")),
                    },
                }

            # Get all questions for these skills with selection status
            questions_query = (
                db.query(
                    Question,
                    Skill.name.label("skill_name"),
                    AssessmentQuestion.question_id.label("selected_question_id"),
                )
                .join(Skill, Question.skill_id == Skill.id)
                .outerjoin(
                    AssessmentQuestion,
                    and_(
                        Question.que_id == AssessmentQuestion.question_id,
                        AssessmentQuestion.assessment_id == assessment_id,
                    ),
                )
                .filter(Question.skill_id.in_(skill_ids))
                .order_by(Question.level, Question.time.desc())
            )

            questions_data = questions_query.all()
            questions = []
            for question_data in questions_data:
                question = question_data.Question
                questions.append(
                    {
                        "que_id": question.que_id,
                        "topic": question.topic,
                        "level": question.level,
                        "question": question.question,
                        "options": question.options,
                        "answer": question.answer,
                        "time": question.time,
                        "skill_id": question.skill_id,
                        "skill_name": question_data.skill_name,
                        "selected": question_data.selected_question_id is not None,
                    }
                )

            # Count questions by level
            easy_count = sum(1 for q in questions if q["level"] == "easy")
            intermediate_count = sum(1 for q in questions if q["level"] == "intermediate")
            advanced_count = sum(1 for q in questions if q["level"] == "advanced")

            # Count selected questions by level
            selected_easy = sum(1 for q in questions if q["level"] == "easy" and q["selected"])
            selected_intermediate = sum(1 for q in questions if q["level"] == "intermediate" and q["selected"])
            selected_advanced = sum(1 for q in questions if q["level"] == "advanced" and q["selected"])

            # Get required counts from environment variables
            required_easy = int(os.getenv("EASY_QUESTIONS_COUNT", "6"))
            required_intermediate = int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "6"))
            required_advanced = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "8"))

            return {
                "assessment_id": assessment_id,
                "assessment_name": assessment.name,
                "question_selection_mode": assessment.question_selection_mode,
                "questions": questions,
                "counts": {
                    "easy": easy_count,
                    "intermediate": intermediate_count,
                    "advanced": advanced_count,
                    "selected_easy": selected_easy,
                    "selected_intermediate": selected_intermediate,
                    "selected_advanced": selected_advanced,
                    "required_easy": required_easy,
                    "required_intermediate": required_intermediate,
                    "required_advanced": required_advanced,
                },
            }

    except SQLAlchemyError as e:
        error(f"Error fetching questions for assessment {assessment_id}: {str(e)}")
        return None


# =============================================================================
# ASSESSMENT QUESTION FUNCTIONS (moved from db_manager.py)
# =============================================================================


def get_session_and_assessment_details_by_code(session_code: str):
    """
    Retrieve session and associated assessment details for a given session code.

    Args:
        session_code (str): The session code to look up.

    Returns:
        dict: A dictionary with session_id, assessment_id, assessment_name,
              is_final, user_id, session_status, remaining_time_seconds,
              and attempted_questions if found.
        None: If the session code is not found or an error occurs.
    """
    try:
        with get_db_context() as db:
            # Get session and assessment details
            session_data = (
                db.query(
                    SessionModel.id.label("session_id"),
                    SessionModel.user_id,
                    SessionModel.status.label("session_status"),
                    Assessment.id.label("assessment_id"),
                    Assessment.name.label("assessment_name"),
                    Assessment.is_final,
                    SessionModel.started_at,
                    SessionModel.completed_at,
                    Assessment.duration_minutes,
                )
                .join(Assessment, SessionModel.assessment_id == Assessment.id)
                .filter(SessionModel.code == session_code)
                .first()
            )

            if not session_data:
                return None

            session_dict = {
                "session_id": session_data.session_id,
                "user_id": session_data.user_id,
                "session_status": session_data.session_status,
                "assessment_id": session_data.assessment_id,
                "assessment_name": session_data.assessment_name,
                "is_final": session_data.is_final,
                "started_at": session_data.started_at,
                "completed_at": session_data.completed_at,
                "duration_minutes": session_data.duration_minutes,
            }

            # Calculate remaining time for sessions
            duration_minutes = session_data.duration_minutes or 30
            total_duration_seconds = duration_minutes * 60

            if session_data.session_status == "in_progress" and session_data.started_at:
                # Get current time in UTC
                now = datetime.datetime.now(datetime.timezone.utc)

                # Convert started_at to UTC if it's not already
                started_at = session_data.started_at
                if started_at.tzinfo is None:
                    started_at = started_at.replace(tzinfo=datetime.timezone.utc)

                # Calculate elapsed time since session started
                elapsed_time = now - started_at
                elapsed_seconds = int(elapsed_time.total_seconds())

                # Calculate remaining time
                remaining_seconds = max(0, total_duration_seconds - elapsed_seconds)
                session_dict["remaining_time_seconds"] = remaining_seconds
            else:
                # For non-in-progress sessions, set default duration
                session_dict["remaining_time_seconds"] = total_duration_seconds

            # Get attempted questions for this session
            attempted_questions = (
                db.query(UserAnswer.question_id).filter(UserAnswer.session_id == session_data.session_id).all()
            )
            session_dict["attempted_questions"] = [q.question_id for q in attempted_questions]

            return session_dict

    except SQLAlchemyError as e:
        log_database_error("select", "sessions", e, session_code=session_code)
        return None


def insert_quiz_creation_logs(data: list):
    """
    Insert quiz creation details into the `quiz_creation_logs` table.

    Args:
        data (list): A list of dictionaries, each containing:
            - "user_id" (str): The admin's username.
            - "assessment_description" (str): The assessment description.
            - "assessment_name" (str): The assessment name.
            - "total_questions" (int): The total number of questions in the quiz.
            - "assessment_id" (int): The ID of the created assessment.
    """
    if not isinstance(data, list) or not all(isinstance(entry, dict) for entry in data):
        raise ValueError("`data` must be a list of dictionaries.")

    try:
        with get_db_context() as db:
            for entry in data:
                # Support both old and new column names for backward compatibility
                assessment_name = entry.get("assessment_name") or entry.get("quiz_name")
                assessment_description = entry.get("assessment_description") or entry.get("topic")
                assessment_id = entry.get("assessment_id")

                # Truncate values to fit VARCHAR(255) constraints
                user_id = entry["user_id"][:255] if len(entry["user_id"]) > 255 else entry["user_id"]
                truncated_assessment_name = (
                    assessment_name[:255] if assessment_name and len(assessment_name) > 255 else assessment_name
                )
                truncated_assessment_description = (
                    assessment_description[:255]
                    if assessment_description and len(assessment_description) > 255
                    else assessment_description
                )

                # Create new log entry
                log_entry = QuizCreationLog(
                    user_id=user_id,
                    assessment_name=truncated_assessment_name,
                    assessment_description=truncated_assessment_description,
                    total_questions=entry["total_questions"],
                    assessment_id=assessment_id if assessment_id else None,
                )

                db.add(log_entry)

            db.commit()

    except SQLAlchemyError as e:
        log_database_error("insert", "quiz_creation_logs", e)
    except ValueError as val_error:
        error("Value error during quiz creation log insertion", exception=val_error)
    except Exception as e:
        log_database_error("insert", "quiz_creation_logs", e)
