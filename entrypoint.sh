#!/bin/sh

# Get service selection
SELECTED_SERVICE="${SERVICE:-all}"

echo "ACTUALLY Configuring service: $SELECTED_SERVICE"

# Clear existing services
rm -f /etc/s6-overlay/s6-rc.d/user/contents.d/*

case "$SELECTED_SERVICE" in
    frontend|backend|worker)
        echo "Enabling ONLY $SELECTED_SERVICE"
        touch "/etc/s6-overlay/s6-rc.d/user/contents.d/$SELECTED_SERVICE"
        ;;
    all)
        echo "Enabling ALL services"
        touch /etc/s6-overlay/s6-rc.d/user/contents.d/frontend
        touch /etc/s6-overlay/s6-rc.d/user/contents.d/backend
        touch /etc/s6-overlay/s6-rc.d/user/contents.d/worker
        ;;
    *)
        echo "Invalid service: $SELECTED_SERVICE" >&2
        echo "Valid options: frontend, backend, worker, all" >&2
        exit 1
        ;;
esac

# Transfer control to S6
exec /init
