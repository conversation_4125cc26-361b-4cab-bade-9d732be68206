name: <PERSON><PERSON>, <PERSON><PERSON>, and Push Image

on:
  push:
    branches:
      - main
      - staging

    paths-ignore:
      - '**/*.md'


jobs:
  pre-commit-lint:
    runs-on: vm-hosted

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.10"

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Install Python dependencies
        run: pip install pre-commit flake8 black

      - name: Install frontend dependencies
        working-directory: frontend
        run: npm ci

      - name: Run Pre-commit Hooks
        run: pre-commit run --all-files --show-diff-on-failure

  build-and-push:
    name: Build and Push Docker Image
    runs-on: vm-hosted
    needs: pre-commit-lint

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Create Image Tag
        id: tag
        run: echo "tag=${{ github.ref_name }}-$(git rev-parse --short HEAD)-$(date +%s)" >> $GITHUB_OUTPUT

      - name: Login to AWS ECR
        uses: docker/login-action@v3
        with:
          registry: ${{ secrets.AWS_ECR_REGISTRY }}
          username: ${{ secrets.AWS_ECR_ACCESS_KEY_ID }}
          password: ${{ secrets.AWS_ECR_SECRET_ACCESS_KEY }}

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          push: true
          tags: ${{ secrets.AWS_ECR_REGISTRY }}/${{ vars.AWS_ECR_REPOSITORY }}:${{ steps.tag.outputs.tag }}

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        if: always()
        env:
          SLACK_WEBHOOK: ${{ secrets.WEBHOOK_URL }}
          SLACK_COLOR: ${{ job.status }}
          SLACK_FOOTER: ""
          SLACK_MESSAGE: "Docker image build **${{ job.status }}**"
