# ------------ Stage 1: S6 + Dockerize Builder ------------
FROM alpine:3.22 AS s6builder

ARG S6_OVERLAY_VERSION="3.2.1.0"
ARG S6_ARCH="x86_64"
ARG DOCKERIZE_VERSION="v0.9.3"

RUN apk add --no-cache curl tar xz

# Download all s6 components
ADD https://github.com/just-containers/s6-overlay/releases/download/v${S6_OVERLAY_VERSION}/s6-overlay-noarch.tar.xz /tmp/
ADD https://github.com/just-containers/s6-overlay/releases/download/v${S6_OVERLAY_VERSION}/s6-overlay-${S6_ARCH}.tar.xz /tmp/
ADD https://github.com/just-containers/s6-overlay/releases/download/v${S6_OVERLAY_VERSION}/s6-overlay-symlinks-noarch.tar.xz /tmp/
ADD https://github.com/just-containers/s6-overlay/releases/download/v${S6_OVERLAY_VERSION}/s6-overlay-symlinks-arch.tar.xz /tmp/

# Download dockerize
RUN wget https://github.com/jwilder/dockerize/releases/download/$DOCKERIZE_VERSION/dockerize-alpine-linux-amd64-$DOCKERIZE_VERSION.tar.gz \
    && mkdir -p /out/usr/local/bin \
    && tar -C /out/usr/local/bin -xzvf dockerize-alpine-linux-amd64-$DOCKERIZE_VERSION.tar.gz \
    && rm dockerize-alpine-linux-amd64-$DOCKERIZE_VERSION.tar.gz

# Extract s6 to a separate directory to copy later
RUN mkdir -p /out/ \
    && tar -C /out/ -Jxpf /tmp/s6-overlay-noarch.tar.xz \
    && tar -C /out/ -Jxpf /tmp/s6-overlay-${S6_ARCH}.tar.xz \
    && tar -C /out/ -Jxpf /tmp/s6-overlay-symlinks-noarch.tar.xz \
    && tar -C /out/ -Jxpf /tmp/s6-overlay-symlinks-arch.tar.xz

# ------------ Stage 2: Frontend Build ------------
FROM node:23.11-alpine AS frontend
WORKDIR /app
COPY frontend/package*.json ./
RUN npm install
COPY frontend .
RUN npm run build

# ------------ Stage 3: Backend Build ------------
FROM python:3.13-slim AS backend
ENV PYTHONUNBUFFERED=1 PIP_NO_CACHE_DIR=1 PIP_DISABLE_PIP_VERSION_CHECK=1 PYTHONDONTWRITEBYTECODE=1
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN apt-get update && apt-get install -y --no-install-recommends libpq-dev build-essential jq shc curl && rm -rf /var/lib/apt/lists/*
WORKDIR /app
COPY backend/requirements.txt .
RUN pip install --upgrade pip && pip install -r requirements.txt
COPY backend .

# ------------ Stage 4: Worker Build ------------
FROM python:3.11-slim AS worker
WORKDIR /app
RUN apt-get update && apt-get install -y gcc postgresql-client && rm -rf /var/lib/apt/lists/*
COPY backend/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY backend .

# ------------ Final Stage ------------
FROM alpine:3.22

# Copy application files from other stages
COPY --from=s6builder /out/ /
COPY --from=frontend /app /app/frontend
COPY --from=backend /app /app/backend
COPY --from=worker /app /app/worker

# Install runtime dependencies (not build tools)
RUN apk add --no-cache python3 py3-pip nodejs npm postgresql-client libpq-dev \
    && pip install --upgrade pip --break-system-packages \
    && pip install --break-system-packages -r /app/backend/requirements.txt

# Copy s6 service definitions and entrypoint
COPY s6-rc.d/ /etc/s6-overlay/s6-rc.d/
COPY entrypoint.sh /entrypoint.sh

RUN chmod +x /entrypoint.sh \
    && chmod +x /etc/s6-overlay/s6-rc.d/*/run

# Verify the services
RUN test -f /etc/s6-overlay/s6-rc.d/backend/run && \
    test -f /etc/s6-overlay/s6-rc.d/frontend/run && \
    test -f /etc/s6-overlay/s6-rc.d/worker/run

WORKDIR /app
EXPOSE 5173 8000 8001

ENTRYPOINT ["/entrypoint.sh"]
